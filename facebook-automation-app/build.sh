#!/bin/bash

# Facebook Automation App Build Script
# This script helps build the application for development and production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    if ! command_exists python3; then
        print_error "Python 3 is not installed. Please install Python 3.8+ from https://python.org/"
        exit 1
    fi
    
    if ! command_exists pip3; then
        print_error "pip3 is not installed. Please install pip3."
        exit 1
    fi
    
    print_success "All prerequisites are installed."
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Create .env if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_status "Creating .env file from .env.example..."
            cp .env.example .env
            print_warning "Please edit .env file with your configuration."
        else
            print_warning ".env.example not found. Please create .env file manually."
        fi
    fi
    
    cd ..
    print_success "Backend setup completed."
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    cd ..
    print_success "Frontend setup completed."
}

# Build frontend for production
build_frontend() {
    print_status "Building frontend for production..."
    
    cd frontend
    npm run build
    cd ..
    
    print_success "Frontend build completed."
}

# Build desktop app
build_desktop() {
    print_status "Building desktop application..."
    
    cd frontend
    
    # Check if electron-builder is available
    if ! npm list electron-builder >/dev/null 2>&1; then
        print_error "electron-builder not found. Installing..."
        npm install --save-dev electron-builder
    fi
    
    npm run electron-build
    cd ..
    
    print_success "Desktop application build completed."
}

# Start development servers
start_dev() {
    print_status "Starting development servers..."
    
    # Start backend in background
    print_status "Starting backend server..."
    cd backend
    source venv/bin/activate
    python main.py &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend
    print_status "Starting frontend development server..."
    cd frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    print_success "Development servers started!"
    print_status "Backend PID: $BACKEND_PID"
    print_status "Frontend PID: $FRONTEND_PID"
    print_status "Frontend: http://localhost:3000"
    print_status "Backend API: http://localhost:8000"
    print_status "API Docs: http://localhost:8000/docs"
    
    # Wait for user input to stop servers
    echo ""
    print_warning "Press Ctrl+C to stop all servers..."
    
    # Trap Ctrl+C to clean up processes
    trap 'print_status "Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT
    
    # Wait indefinitely
    while true; do
        sleep 1
    done
}

# Clean build artifacts
clean() {
    print_status "Cleaning build artifacts..."
    
    # Clean frontend
    if [ -d "frontend/build" ]; then
        rm -rf frontend/build
        print_status "Removed frontend/build"
    fi
    
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        print_status "Removed frontend/dist"
    fi
    
    if [ -d "frontend/node_modules" ]; then
        rm -rf frontend/node_modules
        print_status "Removed frontend/node_modules"
    fi
    
    # Clean backend
    if [ -d "backend/venv" ]; then
        rm -rf backend/venv
        print_status "Removed backend/venv"
    fi
    
    if [ -d "backend/__pycache__" ]; then
        rm -rf backend/__pycache__
        print_status "Removed backend/__pycache__"
    fi
    
    # Clean Python cache files
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -type f -name "*.pyc" -delete 2>/dev/null || true
    
    print_success "Cleanup completed."
}

# Show help
show_help() {
    echo "Facebook Automation App Build Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup       Setup both backend and frontend dependencies"
    echo "  dev         Start development servers"
    echo "  build       Build frontend for production"
    echo "  desktop     Build desktop application"
    echo "  clean       Clean all build artifacts and dependencies"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup     # Initial setup"
    echo "  $0 dev       # Start development"
    echo "  $0 build     # Production build"
    echo "  $0 desktop   # Build desktop app"
    echo ""
}

# Main script logic
main() {
    case "${1:-help}" in
        "setup")
            check_prerequisites
            setup_backend
            setup_frontend
            print_success "Setup completed! Run '$0 dev' to start development servers."
            ;;
        "dev")
            start_dev
            ;;
        "build")
            check_prerequisites
            build_frontend
            ;;
        "desktop")
            check_prerequisites
            build_desktop
            ;;
        "clean")
            clean
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
