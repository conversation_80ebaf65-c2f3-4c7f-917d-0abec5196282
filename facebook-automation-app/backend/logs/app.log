2025-07-06 09:16:16 | INFO | __main__:<module>:177 | Starting server on 127.0.0.1:8000
2025-07-06 09:16:17 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:16:17 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:16:17 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:16:17 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:18:11 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:18:11 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | __main__:<module>:177 | Starting server on 127.0.0.1:8000
2025-07-06 09:18:31 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:18:31 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:10 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:10 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:21 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:51 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:51 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:05 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:05 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:20 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:33 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:33 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:05 | INFO | __main__:<module>:227 | Starting server on 127.0.0.1:8000
2025-07-06 09:41:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:06 | ERROR | database.connection:check_database_connection:76 | Database connection failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-06 09:41:06 | ERROR | database.connection:check_database_connection:76 | Database connection failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-06 09:41:06 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 09:41:06 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 09:41:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769668791-4413108288: GET /api/analytics/dashboard
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769668791-4413108288: GET /api/analytics/dashboard
2025-07-06 09:41:08 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:08 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:08 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:08 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769668791-4413108288: 500
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769668791-4413108288: 500
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751769669809-4416528832: GET /api/analytics/dashboard
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751769669809-4416528832: GET /api/analytics/dashboard
2025-07-06 09:41:09 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:09 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:09 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:09 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751769669809-4416528832: 500
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751769669809-4416528832: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671916-4416567376: GET /api/analytics/dashboard
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671916-4416567376: GET /api/analytics/dashboard
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671926-4417687712: GET /api/profiles
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671926-4417687712: GET /api/profiles
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671927-4417710880: GET /api/campaigns
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671927-4417710880: GET /api/campaigns
2025-07-06 09:41:11 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671929-4417779264: GET /api/scraping/sessions
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671929-4417779264: GET /api/scraping/sessions
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671926-4417687712: 200
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671926-4417687712: 200
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671927-4417710880: 307
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671927-4417710880: 307
2025-07-06 09:41:11 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671916-4416567376: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671916-4416567376: 500
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671929-4417779264: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671929-4417779264: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671953-4416419680: GET /api/campaigns/
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671953-4416419680: GET /api/campaigns/
2025-07-06 09:41:11 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671953-4416419680: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671953-4416419680: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418204336: GET /api/analytics/dashboard
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418204336: GET /api/analytics/dashboard
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418221440: GET /api/scraping/sessions
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418221440: GET /api/scraping/sessions
2025-07-06 09:41:12 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418204336: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418204336: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418221440: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418221440: 500
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673019-4418222208: GET /api/campaigns
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673019-4418222208: GET /api/campaigns
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673019-4418222208: 307
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673019-4418222208: 307
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673023-4418081792: GET /api/campaigns/
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673023-4418081792: GET /api/campaigns/
2025-07-06 09:41:13 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:13 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:13 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:13 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673023-4418081792: 500
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673023-4418081792: 500
2025-07-06 09:41:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:41:43 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:41:43 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:41:43 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:41:43 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:41:43 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:41:43 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:43 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751769703325-**********: GET /api/analytics/dashboard
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751769703325-**********: GET /api/analytics/dashboard
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751769703325-**********: 200
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751769703325-**********: 200
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751769713783-**********: GET /health
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751769713783-**********: GET /health
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751769713783-**********: 200
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751769713783-**********: 200
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769733352-**********: GET /api/analytics/dashboard
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769733352-**********: GET /api/analytics/dashboard
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769733352-**********: 200
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769733352-**********: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734721-**********: GET /api/analytics/dashboard
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734721-**********: GET /api/analytics/dashboard
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419901376: GET /api/profiles
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419901376: GET /api/profiles
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419903344: GET /api/campaigns
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419903344: GET /api/campaigns
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419950432: GET /api/scraping/sessions
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419950432: GET /api/scraping/sessions
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419901376: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419901376: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419903344: 307
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419903344: 307
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734721-**********: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734721-**********: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419950432: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419950432: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734745-4412821408: GET /api/campaigns/
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734745-4412821408: GET /api/campaigns/
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734745-4412821408: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734745-4412821408: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4412820928: GET /api/analytics/dashboard
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4412820928: GET /api/analytics/dashboard
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4419769200: GET /api/profiles
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4419769200: GET /api/profiles
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419867888: GET /api/campaigns
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419867888: GET /api/campaigns
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419903056: GET /api/scraping/sessions
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419903056: GET /api/scraping/sessions
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4419769200: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4419769200: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419867888: 307
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419867888: 307
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4412820928: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4412820928: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419903056: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419903056: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748575-4419899456: GET /api/campaigns/
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748575-4419899456: GET /api/campaigns/
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748575-4419899456: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748575-4419899456: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753708-4419771120: GET /api/analytics/dashboard
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753708-4419771120: GET /api/analytics/dashboard
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419912704: GET /api/profiles
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419912704: GET /api/profiles
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419951104: GET /api/campaigns
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419951104: GET /api/campaigns
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419984496: GET /api/scraping/sessions
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419984496: GET /api/scraping/sessions
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419912704: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419912704: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419951104: 307
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419951104: 307
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753708-4419771120: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753708-4419771120: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419984496: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419984496: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753721-4419866928: GET /api/campaigns/
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753721-4419866928: GET /api/campaigns/
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753721-4419866928: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753721-4419866928: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757432-4419982576: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757432-4419982576: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4419982528: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4419982528: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4420524352: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4420524352: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757435-4420549072: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757435-4420549072: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757436-4420575584: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757436-4420575584: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757432-4419982576: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757432-4419982576: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757437-4420611520: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757437-4420611520: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4419982528: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4419982528: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757436-4420575584: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757436-4420575584: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757437-4420611520: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757437-4420611520: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4420524352: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4420524352: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757435-4420549072: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757435-4420549072: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757466-4420466816: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757466-4420466816: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757468-4420527296: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757468-4420527296: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757466-4420466816: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757466-4420466816: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757468-4420527296: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757468-4420527296: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760789-4420427840: GET /api/analytics/dashboard
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760789-4420427840: GET /api/analytics/dashboard
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420428272: GET /api/profiles
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420428272: GET /api/profiles
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420526144: GET /api/scraping/sessions
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420526144: GET /api/scraping/sessions
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420510672: GET /api/campaigns
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420510672: GET /api/campaigns
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420428272: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420428272: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420510672: 307
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420510672: 307
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760789-4420427840: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760789-4420427840: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420526144: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420526144: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760801-4420431392: GET /api/campaigns/
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760801-4420431392: GET /api/campaigns/
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760801-4420431392: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760801-4420431392: 200
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751769921189-4420512736: GET /api/profiles
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751769921189-4420512736: GET /api/profiles
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751769921189-4420512736: 200
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751769921189-4420512736: 200
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769927369-4419901040: GET /api/profiles/stats
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769927369-4419901040: GET /api/profiles/stats
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769927369-4419901040: 200
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769927369-4419901040: 200
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751769935127-4420511008: POST /api/profiles
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751769935127-4420511008: POST /api/profiles
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751769935127-4420511008: 200
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751769935127-4420511008: 200
2025-07-06 09:45:57 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:45:57 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:45:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:45:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:45:58 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:45:58 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:45:58 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:45:58 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:45:58 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:45:58 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:45:58 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:45:58 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769968358-4360631872: POST /api/profiles
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769968358-4360631872: POST /api/profiles
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769968358-4360631872: 307
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769968358-4360631872: 307
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751769978929-**********: POST /api/profiles
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751769978929-**********: POST /api/profiles
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751769978929-**********: 307
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751769978929-**********: 307
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769987532-**********: GET /health
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769987532-**********: GET /health
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769987532-**********: 200
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769987532-**********: 200
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751769994111-**********: POST /api/profiles
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751769994111-**********: POST /api/profiles
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751769994111-**********: 307
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751769994111-**********: 307
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751770001207-**********: POST /api/profiles/
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751770001207-**********: POST /api/profiles/
2025-07-06 09:46:41 | INFO | api.profiles:create_profile:93 | Created new profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 09:46:41 | INFO | api.profiles:create_profile:93 | Created new profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751770001207-**********: 200
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751770001207-**********: 200
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751770007557-4363761552: GET /api/profiles/
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751770007557-4363761552: GET /api/profiles/
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751770007557-4363761552: 200
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751770007557-4363761552: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014256-4364132752: GET /api/analytics/dashboard
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014256-4364132752: GET /api/analytics/dashboard
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014257-4360663680: GET /api/profiles
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014257-4360663680: GET /api/profiles
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014258-4364174432: GET /api/campaigns
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014258-4364174432: GET /api/campaigns
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014259-4364195776: GET /api/scraping/sessions
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014259-4364195776: GET /api/scraping/sessions
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014257-4360663680: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014257-4360663680: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014258-4364174432: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014258-4364174432: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014256-4364132752: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014256-4364132752: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014259-4364195776: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014259-4364195776: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014299-4362963936: GET /api/profiles/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014299-4362963936: GET /api/profiles/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014300-4364173712: GET /api/campaigns/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014300-4364173712: GET /api/campaigns/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014299-4362963936: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014299-4362963936: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014300-4364173712: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014300-4364173712: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021203-4364132464: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021203-4364132464: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021203-4364132464: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021203-4364132464: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021205-4364197408: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021205-4364197408: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021205-4364197408: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021205-4364197408: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021223-4363845792: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021223-4363845792: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021223-4363845792: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021223-4363845792: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021229-4364116704: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021229-4364116704: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021229-4364116704: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021229-4364116704: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025609-4365288256: GET /api/analytics/dashboard
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025609-4365288256: GET /api/analytics/dashboard
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025610-4365429392: GET /api/profiles
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025610-4365429392: GET /api/profiles
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365430544: GET /api/campaigns
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365430544: GET /api/campaigns
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365087072: GET /api/scraping/sessions
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365087072: GET /api/scraping/sessions
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025610-4365429392: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025610-4365429392: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365430544: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365430544: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025609-4365288256: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025609-4365288256: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365087072: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365087072: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025623-4365290272: GET /api/profiles/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025623-4365290272: GET /api/profiles/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025624-4365528560: GET /api/campaigns/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025624-4365528560: GET /api/campaigns/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025623-4365290272: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025623-4365290272: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025624-4365528560: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025624-4365528560: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028873-4363760400: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028873-4363760400: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028874-4365569136: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028874-4365569136: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028873-4363760400: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028873-4363760400: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028874-4365569136: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028874-4365569136: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028880-4365596560: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028880-4365596560: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028880-4365596560: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028880-4365596560: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028881-4365598576: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028881-4365598576: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028881-4365598576: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028881-4365598576: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028888-4364132704: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028888-4364132704: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028888-4364132704: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028888-4364132704: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028892-4365633328: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028892-4365633328: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028892-4365633328: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028892-4365633328: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047404-4365670240: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047404-4365670240: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047406-4365443376: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047406-4365443376: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047404-4365670240: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047404-4365670240: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047406-4365443376: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047406-4365443376: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047412-4365598672: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047412-4365598672: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047412-4365598672: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047412-4365598672: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047420-4360663200: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047420-4360663200: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047420-4360663200: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047420-4360663200: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047425-4365360816: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047425-4365360816: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047425-4365360816: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047425-4365360816: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047439-4364117088: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047439-4364117088: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047439-4364117088: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047439-4364117088: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064524-4365446208: GET /api/analytics/dashboard
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064524-4365446208: GET /api/analytics/dashboard
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365595312: GET /api/profiles
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365595312: GET /api/profiles
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365428144: GET /api/campaigns
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365428144: GET /api/campaigns
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064528-4364175296: GET /api/scraping/sessions
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064528-4364175296: GET /api/scraping/sessions
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365595312: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365595312: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365428144: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365428144: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064524-4365446208: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064524-4365446208: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064528-4364175296: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064528-4364175296: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4362961056: GET /api/campaigns/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4362961056: GET /api/campaigns/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4365430400: GET /api/profiles/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4365430400: GET /api/profiles/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4362961056: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4362961056: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4365430400: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4365430400: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075050-4364135296: GET /api/analytics/dashboard
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075050-4364135296: GET /api/analytics/dashboard
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075051-4365666960: GET /api/profiles
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075051-4365666960: GET /api/profiles
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365689376: GET /api/campaigns
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365689376: GET /api/campaigns
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365691248: GET /api/scraping/sessions
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365691248: GET /api/scraping/sessions
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075051-4365666960: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075051-4365666960: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365689376: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365689376: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075050-4364135296: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075050-4364135296: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365691248: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365691248: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365666336: GET /api/campaigns/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365666336: GET /api/campaigns/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365691536: GET /api/profiles/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365691536: GET /api/profiles/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365666336: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365666336: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365691536: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365691536: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083695-4364136112: GET /api/analytics/dashboard
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083695-4364136112: GET /api/analytics/dashboard
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365778368: GET /api/profiles
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365778368: GET /api/profiles
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365903184: GET /api/campaigns
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365903184: GET /api/campaigns
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083698-4365905056: GET /api/scraping/sessions
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083698-4365905056: GET /api/scraping/sessions
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365778368: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365778368: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365903184: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365903184: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083695-4364136112: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083695-4364136112: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083698-4365905056: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083698-4365905056: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365889200: GET /api/profiles/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365889200: GET /api/profiles/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365843664: GET /api/campaigns/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365843664: GET /api/campaigns/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365889200: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365889200: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365843664: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365843664: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090648-4365739440: GET /api/analytics/dashboard
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090648-4365739440: GET /api/analytics/dashboard
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090649-4365905488: GET /api/profiles
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090649-4365905488: GET /api/profiles
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365887136: GET /api/campaigns
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365887136: GET /api/campaigns
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365843040: GET /api/scraping/sessions
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365843040: GET /api/scraping/sessions
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090649-4365905488: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090649-4365905488: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365887136: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365887136: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090648-4365739440: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090648-4365739440: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365843040: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365843040: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090687-4365886752: GET /api/profiles/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090687-4365886752: GET /api/profiles/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090688-4365782704: GET /api/campaigns/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090688-4365782704: GET /api/campaigns/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090687-4365886752: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090687-4365886752: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090688-4365782704: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090688-4365782704: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134946-4365666960: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134946-4365666960: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4364232736: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4364232736: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4365842800: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4365842800: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134949-4365741744: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134949-4365741744: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134946-4365666960: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134946-4365666960: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4364232736: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4364232736: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4365842800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4365842800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134949-4365741744: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134949-4365741744: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4365778800: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4365778800: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4364231056: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4364231056: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4365778800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4365778800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4364231056: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4364231056: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182417-4363846320: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182417-4363846320: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182418-4365444000: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182418-4365444000: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182417-4363846320: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182417-4363846320: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182418-4365444000: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182418-4365444000: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182425-4365012608: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182425-4365012608: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182425-4365012608: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182425-4365012608: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182437-4365691008: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182437-4365691008: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182437-4365691008: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182437-4365691008: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182441-4365710336: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182441-4365710336: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182441-4365710336: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182441-4365710336: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182450-4363847808: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182450-4363847808: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182450-4363847808: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182450-4363847808: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184777-4365904624: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184777-4365904624: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184777-4365904624: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184777-4365904624: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184778-4364132752: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184778-4364132752: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184778-4364132752: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184778-4364132752: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184797-4365009968: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184797-4365009968: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184797-4365009968: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184797-4365009968: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184803-4365664656: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184803-4365664656: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184803-4365664656: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184803-4365664656: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201514-4365445632: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201514-4365445632: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4365289264: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4365289264: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4360319712: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4360319712: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201514-4365445632: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201514-4365445632: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4360319712: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4360319712: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4365289264: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4365289264: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201519-4365923856: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201519-4365923856: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201519-4365923856: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201519-4365923856: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365926016: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365926016: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365710240: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365710240: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365926016: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365926016: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365710240: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365710240: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201530-4364134912: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201530-4364134912: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201530-4364134912: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201530-4364134912: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201534-4364177072: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201534-4364177072: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201534-4364177072: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201534-4364177072: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201538-4365971808: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201538-4365971808: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201538-4365971808: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201538-4365971808: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201543-4365778224: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201543-4365778224: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201543-4365778224: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201543-4365778224: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205298-4360664208: GET /api/analytics/dashboard
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205298-4360664208: GET /api/analytics/dashboard
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205300-4365782464: GET /api/profiles
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205300-4365782464: GET /api/profiles
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365902080: GET /api/campaigns
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365902080: GET /api/campaigns
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365844192: GET /api/scraping/sessions
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365844192: GET /api/scraping/sessions
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205300-4365782464: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205300-4365782464: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365902080: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365902080: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205298-4360664208: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205298-4360664208: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365844192: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365844192: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365902512: GET /api/campaigns/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365902512: GET /api/campaigns/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365569616: GET /api/profiles/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365569616: GET /api/profiles/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365902512: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365902512: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365569616: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365569616: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209668-4365569280: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209668-4365569280: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209669-4362853248: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209669-4362853248: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209668-4365569280: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209668-4365569280: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209669-4362853248: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209669-4362853248: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209674-4365777648: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209674-4365777648: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209674-4365777648: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209674-4365777648: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209678-4365973680: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209678-4365973680: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209678-4365973680: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209678-4365973680: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209687-4365944432: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209687-4365944432: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209687-4365944432: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209687-4365944432: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209695-4365568560: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209695-4365568560: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209695-4365568560: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209695-4365568560: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215170-4365843280: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215170-4365843280: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215170-4365843280: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215170-4365843280: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215172-4365670240: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215172-4365670240: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215172-4365670240: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215172-4365670240: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215188-4365946592: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215188-4365946592: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215188-4365946592: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215188-4365946592: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215196-4365634768: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215196-4365634768: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215196-4365634768: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215196-4365634768: 200
2025-07-06 09:51:16 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:51:16 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276277-4365089952: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276277-4365089952: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276277-4365089952: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276277-4365089952: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276279-4365528560: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276279-4365528560: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276279-4365528560: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276279-4365528560: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276296-4365842464: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276296-4365842464: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276296-4365842464: 200
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276296-4365842464: 200
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276303-4366035168: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276303-4366035168: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276303-4366035168: 200
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276303-4366035168: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282057-4365634624: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282057-4365634624: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282057-4365634624: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282057-4365634624: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282061-4365426800: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282061-4365426800: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282061-4365426800: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282061-4365426800: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282067-4360252192: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282067-4360252192: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282067-4360252192: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282067-4360252192: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282076-4365672304: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282076-4365672304: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282076-4365672304: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282076-4365672304: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283276-4364194048: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283276-4364194048: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283276-4364194048: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283276-4364194048: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283279-4365567792: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283279-4365567792: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283279-4365567792: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283279-4365567792: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283289-4364197600: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283289-4364197600: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283289-4364197600: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283289-4364197600: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283297-4362963456: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283297-4362963456: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283297-4362963456: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283297-4362963456: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288851-4365089952: GET /api/analytics/dashboard
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288851-4365089952: GET /api/analytics/dashboard
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288852-4365778512: GET /api/scraping/sessions
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288852-4365778512: GET /api/scraping/sessions
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288851-4365089952: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288851-4365089952: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288852-4365778512: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288852-4365778512: 200
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751770318879-4365886512: GET /api/analytics/dashboard
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751770318879-4365886512: GET /api/analytics/dashboard
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751770318879-4365886512: 200
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751770318879-4365886512: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333355-4365777168: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333355-4365777168: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333355-4365777168: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333355-4365777168: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333357-4365944432: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333357-4365944432: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333357-4365944432: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333357-4365944432: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333374-4365971760: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333374-4365971760: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333374-4365971760: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333374-4365971760: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333382-4364194480: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333382-4364194480: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333382-4364194480: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333382-4364194480: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336716-4365568272: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336716-4365568272: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336716-4365568272: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336716-4365568272: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336719-4365528368: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336719-4365528368: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336719-4365528368: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336719-4365528368: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336726-4365974160: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336726-4365974160: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336726-4365974160: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336726-4365974160: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336736-4365481392: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336736-4365481392: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336736-4365481392: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336736-4365481392: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338556-4365481872: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338556-4365481872: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338556-4365481872: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338556-4365481872: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338557-4365781408: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338557-4365781408: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338557-4365781408: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338557-4365781408: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338567-4365904720: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338567-4365904720: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338567-4365904720: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338567-4365904720: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338571-4365291328: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338571-4365291328: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338571-4365291328: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338571-4365291328: 200
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751770353381-4365887232: POST /api/profiles/
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751770353381-4365887232: POST /api/profiles/
2025-07-06 09:52:33 | INFO | api.profiles:create_profile:93 | Created new profile: Demo Profile (4fd6972b-b736-40c3-80d1-815c898bc788)
2025-07-06 09:52:33 | INFO | api.profiles:create_profile:93 | Created new profile: Demo Profile (4fd6972b-b736-40c3-80d1-815c898bc788)
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751770353381-4365887232: 200
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751770353381-4365887232: 200
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770360853-4365358656: GET /api/profiles/
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770360853-4365358656: GET /api/profiles/
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770360853-4365358656: 200
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770360853-4365358656: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369030-4365739584: GET /api/analytics/dashboard
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369030-4365739584: GET /api/analytics/dashboard
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365711536: GET /api/profiles
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365711536: GET /api/profiles
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365779392: GET /api/campaigns
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365779392: GET /api/campaigns
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365482256: GET /api/scraping/sessions
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365482256: GET /api/scraping/sessions
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365711536: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365711536: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365779392: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365779392: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369030-4365739584: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369030-4365739584: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365482256: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365482256: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365357264: GET /api/campaigns/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365357264: GET /api/campaigns/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365291136: GET /api/profiles/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365291136: GET /api/profiles/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365357264: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365357264: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365291136: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365291136: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375551-4365483312: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375551-4365483312: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375551-4365483312: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375551-4365483312: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375554-4365780160: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375554-4365780160: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375554-4365780160: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375554-4365780160: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375574-4364232352: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375574-4364232352: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375574-4364232352: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375574-4364232352: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375582-4365740112: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375582-4365740112: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375582-4365740112: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375582-4365740112: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377413-4365289024: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377413-4365289024: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4365776304: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4365776304: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4363962496: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4363962496: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377413-4365289024: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377413-4365289024: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4363962496: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4363962496: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4365776304: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4365776304: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377418-4363438064: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377418-4363438064: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377418-4363438064: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377418-4363438064: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4365739008: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4365739008: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4360665840: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4360665840: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4365739008: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4365739008: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4360665840: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4360665840: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377426-4365120896: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377426-4365120896: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377426-4365120896: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377426-4365120896: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377433-4365713264: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377433-4365713264: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377438-4365527744: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377438-4365527744: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377433-4365713264: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377433-4365713264: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377438-4365527744: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377438-4365527744: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377443-4365569856: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377443-4365569856: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377443-4365569856: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377443-4365569856: 200
2025-07-06 09:53:18 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:53:18 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:53:20 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 09:53:20 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:53:20 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:53:20 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:53:20 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:53:20 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:53:20 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:53:20 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:53:20 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:53:20 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:53:20 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406619-4369656944: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406619-4369656944: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406622-4369867632: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406622-4369867632: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406624-4369917984: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406624-4369917984: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406619-4369656944: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406619-4369656944: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406624-4369917984: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406624-4369917984: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406622-4369867632: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406622-4369867632: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406693-4373192320: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406693-4373192320: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373263984: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373263984: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373293424: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373293424: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406693-4373192320: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406693-4373192320: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373293424: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373293424: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373263984: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373263984: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406719-4369678544: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406719-4369678544: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406721-4373345664: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406721-4373345664: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406721-4373345664: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406721-4373345664: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406719-4369678544: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406719-4369678544: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406724-4373498320: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406724-4373498320: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373496064: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373496064: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373533024: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373533024: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373496064: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373496064: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406724-4373498320: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406724-4373498320: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406740-4405513424: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406740-4405513424: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373533024: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373533024: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406740-4405513424: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406740-4405513424: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406752-4405511552: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406752-4405511552: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406752-4405511552: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406752-4405511552: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406755-4373460112: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406755-4373460112: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406755-4373460112: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406755-4373460112: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406764-4373532976: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406764-4373532976: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406764-4373532976: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406764-4373532976: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406768-4405635344: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406768-4405635344: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406768-4405635344: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406768-4405635344: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449308-4405467312: GET /api/analytics/dashboard
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449308-4405467312: GET /api/analytics/dashboard
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449312-4373536240: GET /api/profiles
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449312-4373536240: GET /api/profiles
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449314-4405513808: GET /api/campaigns
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449314-4405513808: GET /api/campaigns
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449381-4369794048: GET /api/scraping/sessions
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449381-4369794048: GET /api/scraping/sessions
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449312-4373536240: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449312-4373536240: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449308-4405467312: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449308-4405467312: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449314-4405513808: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449314-4405513808: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449381-4369794048: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449381-4369794048: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373396016: GET /api/profiles/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373396016: GET /api/profiles/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373168032: GET /api/campaigns/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373168032: GET /api/campaigns/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373396016: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373396016: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373168032: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373168032: 200
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770660372-4369867344: GET /api/profiles/
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770660372-4369867344: GET /api/profiles/
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770660372-4369867344: 200
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770660372-4369867344: 200
2025-07-06 09:58:46 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:58:46 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405733264: GET /api/analytics/dashboard
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405733264: GET /api/analytics/dashboard
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405263520: GET /api/profiles
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405263520: GET /api/profiles
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4369974368: GET /api/campaigns
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4369974368: GET /api/campaigns
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4373535616: GET /api/scraping/sessions
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4373535616: GET /api/scraping/sessions
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405263520: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405263520: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4369974368: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4369974368: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405733264: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405733264: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4373535616: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4373535616: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726137-4406075008: GET /api/campaigns/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726137-4406075008: GET /api/campaigns/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726138-4406104176: GET /api/profiles/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726138-4406104176: GET /api/profiles/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726137-4406075008: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726137-4406075008: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726138-4406104176: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726138-4406104176: 200
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770756142-4405733168: GET /api/analytics/dashboard
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770756142-4405733168: GET /api/analytics/dashboard
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770756142-4405733168: 200
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770756142-4405733168: 200
2025-07-06 09:59:30 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
