2025-07-06 09:16:16 | INFO | __main__:<module>:177 | Starting server on 127.0.0.1:8000
2025-07-06 09:16:17 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:16:17 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:16:17 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:16:17 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:18:11 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:18:11 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | __main__:<module>:177 | Starting server on 127.0.0.1:8000
2025-07-06 09:18:31 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:18:31 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:10 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:10 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:21 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:51 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:51 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:05 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:05 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:20 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:33 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:33 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:05 | INFO | __main__:<module>:227 | Starting server on 127.0.0.1:8000
2025-07-06 09:41:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:06 | ERROR | database.connection:check_database_connection:76 | Database connection failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-06 09:41:06 | ERROR | database.connection:check_database_connection:76 | Database connection failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-06 09:41:06 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 09:41:06 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 09:41:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769668791-4413108288: GET /api/analytics/dashboard
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769668791-4413108288: GET /api/analytics/dashboard
2025-07-06 09:41:08 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:08 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:08 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:08 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769668791-4413108288: 500
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769668791-4413108288: 500
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751769669809-4416528832: GET /api/analytics/dashboard
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751769669809-4416528832: GET /api/analytics/dashboard
2025-07-06 09:41:09 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:09 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:09 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:09 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751769669809-4416528832: 500
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751769669809-4416528832: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671916-4416567376: GET /api/analytics/dashboard
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671916-4416567376: GET /api/analytics/dashboard
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671926-4417687712: GET /api/profiles
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671926-4417687712: GET /api/profiles
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671927-4417710880: GET /api/campaigns
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671927-4417710880: GET /api/campaigns
2025-07-06 09:41:11 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671929-4417779264: GET /api/scraping/sessions
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671929-4417779264: GET /api/scraping/sessions
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671926-4417687712: 200
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671926-4417687712: 200
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671927-4417710880: 307
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671927-4417710880: 307
2025-07-06 09:41:11 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671916-4416567376: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671916-4416567376: 500
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671929-4417779264: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671929-4417779264: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671953-4416419680: GET /api/campaigns/
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671953-4416419680: GET /api/campaigns/
2025-07-06 09:41:11 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671953-4416419680: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671953-4416419680: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418204336: GET /api/analytics/dashboard
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418204336: GET /api/analytics/dashboard
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418221440: GET /api/scraping/sessions
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418221440: GET /api/scraping/sessions
2025-07-06 09:41:12 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418204336: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418204336: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418221440: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418221440: 500
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673019-4418222208: GET /api/campaigns
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673019-4418222208: GET /api/campaigns
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673019-4418222208: 307
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673019-4418222208: 307
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673023-4418081792: GET /api/campaigns/
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673023-4418081792: GET /api/campaigns/
2025-07-06 09:41:13 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:13 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:13 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:13 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673023-4418081792: 500
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673023-4418081792: 500
2025-07-06 09:41:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:41:43 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:41:43 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:41:43 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:41:43 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:41:43 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:41:43 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:43 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751769703325-4412945792: GET /api/analytics/dashboard
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751769703325-4412945792: GET /api/analytics/dashboard
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751769703325-4412945792: 200
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751769703325-4412945792: 200
