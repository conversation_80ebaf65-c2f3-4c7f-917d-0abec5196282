#!/usr/bin/env python3
"""
Facebook Automation App - Backend Server
Main FastAPI application entry point
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger
from dotenv import load_dotenv

# Import database components
from database.connection import init_database, check_database_connection

# Import API routes
from api import profiles_router, campaigns_router, scraping_router, messaging_router, analytics_router

# Import middleware
from middleware.error_handler import ErrorHandlerMiddleware
from middleware.performance import PerformanceMiddleware
from middleware.rate_limiter import RateLimiterMiddleware

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Facebook Automation API",
    description="Backend API for Facebook automation desktop application",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(ErrorHandlerMiddleware)
app.add_middleware(PerformanceMiddleware)
app.add_middleware(RateLimiterMiddleware, requests_per_minute=100, requests_per_hour=2000)

# Configure logging
logger.add(
    "logs/app.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting Facebook Automation Backend Server...")

    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Initialize database
    try:
        if check_database_connection():
            init_database()
            logger.info("Database initialized successfully")
        else:
            logger.error("Database connection failed")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")

    logger.info("Backend server started successfully")

# Include API routers
app.include_router(profiles_router)
app.include_router(campaigns_router)
app.include_router(scraping_router)
app.include_router(messaging_router)
app.include_router(analytics_router)

# Performance monitoring endpoints
@app.get("/api/system/performance")
async def get_performance_stats():
    """Get system performance statistics"""
    # Get performance middleware instance
    for middleware in app.user_middleware:
        if isinstance(middleware.cls, type) and issubclass(middleware.cls, PerformanceMiddleware):
            return middleware.cls.get_performance_stats()
    return {"error": "Performance middleware not found"}

@app.get("/api/system/health")
async def get_system_health():
    """Get system health status"""
    from middleware.performance import ResourceMonitor
    monitor = ResourceMonitor()
    return monitor.check_resources()

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Facebook Automation Backend Server...")

# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint - health check"""
    return {
        "message": "Facebook Automation API is running",
        "status": "healthy",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "facebook-automation-api",
        "version": "1.0.0"
    }

# Old API endpoints removed - using new API routers instead

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"message": "Endpoint not found", "status": "error"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error", "status": "error"}
    )

if __name__ == "__main__":
    # Get configuration from environment variables
    host = os.getenv("HOST", "127.0.0.1")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "true").lower() == "true"
    
    logger.info(f"Starting server on {host}:{port}")
    
    # Run the server
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if debug else "warning"
    )
