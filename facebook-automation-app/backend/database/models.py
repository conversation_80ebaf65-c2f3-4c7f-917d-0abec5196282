"""
Database models for Facebook Automation App
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, DateTime, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .connection import Base
import uuid
from datetime import datetime

class Profile(Base):
    """Browser profile model for antidetect functionality"""
    __tablename__ = "profiles"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Browser configuration
    user_agent = Column(Text)
    screen_resolution = Column(String(50))  # e.g., "1920x1080"
    timezone = Column(String(100))
    language = Column(String(10))  # e.g., "en-US"
    
    # Proxy configuration
    proxy_host = Column(String(255))
    proxy_port = Column(Integer)
    proxy_username = Column(String(255))
    proxy_password = Column(String(255))
    proxy_type = Column(String(20))  # http, socks5, etc.
    
    # Facebook account info
    facebook_email = Column(String(255))
    facebook_password = Column(String(255))  # Should be encrypted
    facebook_user_id = Column(String(100))
    facebook_username = Column(String(255))
    
    # Profile status
    status = Column(String(50), default="inactive")  # active, inactive, banned, error
    last_used = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Browser profile path
    profile_path = Column(String(500))
    
    # Relationships
    campaigns = relationship("Campaign", back_populates="profile")
    scraping_sessions = relationship("ScrapingSession", back_populates="profile")
    messages = relationship("Message", back_populates="profile")

class Campaign(Base):
    """Campaign model for organizing automation tasks"""
    __tablename__ = "campaigns"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Campaign configuration
    campaign_type = Column(String(50))  # scraping, messaging, mixed
    target_audience = Column(JSON)  # JSON configuration for targeting
    message_template = Column(Text)
    
    # Scheduling
    scheduled_start = Column(DateTime)
    scheduled_end = Column(DateTime)
    
    # Status and progress
    status = Column(String(50), default="draft")  # draft, active, paused, completed, failed
    progress = Column(Float, default=0.0)  # 0.0 to 100.0
    
    # Statistics
    total_targets = Column(Integer, default=0)
    completed_targets = Column(Integer, default=0)
    successful_actions = Column(Integer, default=0)
    failed_actions = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Foreign keys
    profile_id = Column(String, ForeignKey("profiles.id"))
    
    # Relationships
    profile = relationship("Profile", back_populates="campaigns")
    scraping_sessions = relationship("ScrapingSession", back_populates="campaign")
    messages = relationship("Message", back_populates="campaign")

class ScrapingSession(Base):
    """Scraping session model for tracking UID collection"""
    __tablename__ = "scraping_sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    
    # Scraping configuration
    target_url = Column(Text)  # Facebook page/group URL
    target_type = Column(String(50))  # page, group, post, search
    scraping_method = Column(String(50))  # comments, likes, shares, members
    max_results = Column(Integer, default=1000)
    
    # Filters
    filters = Column(JSON)  # JSON configuration for filtering results
    
    # Status and progress
    status = Column(String(50), default="pending")  # pending, running, completed, failed, paused
    progress = Column(Float, default=0.0)
    
    # Results
    total_found = Column(Integer, default=0)
    total_scraped = Column(Integer, default=0)
    unique_uids = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Error tracking
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # Foreign keys
    profile_id = Column(String, ForeignKey("profiles.id"))
    campaign_id = Column(String, ForeignKey("campaigns.id"))
    
    # Relationships
    profile = relationship("Profile", back_populates="scraping_sessions")
    campaign = relationship("Campaign", back_populates="scraping_sessions")

class Message(Base):
    """Message model for tracking sent messages"""
    __tablename__ = "messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Message content
    content = Column(Text, nullable=False)
    message_type = Column(String(50), default="text")  # text, image, video, link
    
    # Target information
    target_uid = Column(String(100))
    target_name = Column(String(255))
    target_url = Column(Text)
    
    # Message status
    status = Column(String(50), default="pending")  # pending, sent, delivered, failed, blocked
    
    # Delivery information
    sent_at = Column(DateTime)
    delivered_at = Column(DateTime)
    read_at = Column(DateTime)
    
    # Error tracking
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Foreign keys
    profile_id = Column(String, ForeignKey("profiles.id"))
    campaign_id = Column(String, ForeignKey("campaigns.id"))
    
    # Relationships
    profile = relationship("Profile", back_populates="messages")
    campaign = relationship("Campaign", back_populates="messages")

class Analytics(Base):
    """Analytics model for tracking performance metrics"""
    __tablename__ = "analytics"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Metric information
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_type = Column(String(50))  # count, rate, duration, etc.
    
    # Dimensions
    profile_id = Column(String, ForeignKey("profiles.id"))
    campaign_id = Column(String, ForeignKey("campaigns.id"))
    date = Column(DateTime, default=func.now())
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    
    # Indexes for better query performance
    __table_args__ = (
        {"sqlite_autoincrement": True}
    )
