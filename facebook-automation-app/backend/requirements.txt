# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1

# Async & Concurrency
asyncio-atexit==1.0.1

# Data Processing
pandas>=2.1.4,<3.0.0  # Updated for better Python 3.13 compatibility
openpyxl==3.1.2

# HTTP Client
httpx==0.25.2

# Validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Security
cryptography>=41.0.0,<46.0.0  # Updated for better compatibility
python-multipart==0.0.6

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
psutil==5.9.6

# Browser Automation (local zendriver)
# zendriver will be installed from local path

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
