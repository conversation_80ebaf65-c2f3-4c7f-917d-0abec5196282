"""
Performance monitoring middleware
"""

import time
import psutil
import asyncio
from typing import Callable, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
from collections import defaultdict, deque
import threading

class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for monitoring performance metrics"""
    
    def __init__(self, app, max_history: int = 1000):
        super().__init__(app)
        self.max_history = max_history
        self.request_times = deque(maxlen=max_history)
        self.endpoint_stats = defaultdict(lambda: {
            "count": 0,
            "total_time": 0.0,
            "min_time": float('inf'),
            "max_time": 0.0,
            "errors": 0
        })
        self.system_stats = {
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "disk_usage": 0.0
        }
        
        # Start background system monitoring
        self._start_system_monitoring()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Record start time
        start_time = time.time()
        
        # Get endpoint identifier
        endpoint = f"{request.method} {request.url.path}"
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Store response time in request state
            request.state.response_time = response_time
            
            # Update statistics
            self._update_stats(endpoint, response_time, response.status_code)
            
            # Add performance headers
            response.headers["X-Response-Time"] = f"{response_time:.3f}s"
            response.headers["X-Request-ID"] = getattr(request.state, "request_id", "unknown")
            
            # Log slow requests
            if response_time > 5.0:  # Log requests taking more than 5 seconds
                logger.warning(
                    f"Slow request: {endpoint} took {response_time:.3f}s",
                    extra={
                        "endpoint": endpoint,
                        "response_time": response_time,
                        "status_code": response.status_code
                    }
                )
            
            return response
            
        except Exception as e:
            # Calculate response time even for errors
            response_time = time.time() - start_time
            
            # Update error statistics
            self._update_stats(endpoint, response_time, 500, error=True)
            
            # Re-raise the exception
            raise e
    
    def _update_stats(self, endpoint: str, response_time: float, status_code: int, error: bool = False):
        """Update performance statistics"""
        try:
            # Update request times history
            self.request_times.append(response_time)
            
            # Update endpoint statistics
            stats = self.endpoint_stats[endpoint]
            stats["count"] += 1
            stats["total_time"] += response_time
            stats["min_time"] = min(stats["min_time"], response_time)
            stats["max_time"] = max(stats["max_time"], response_time)
            
            if error or status_code >= 400:
                stats["errors"] += 1
            
        except Exception as e:
            logger.error(f"Error updating performance stats: {e}")
    
    def _start_system_monitoring(self):
        """Start background system monitoring"""
        def monitor_system():
            while True:
                try:
                    self.system_stats["cpu_percent"] = psutil.cpu_percent(interval=1)
                    self.system_stats["memory_percent"] = psutil.virtual_memory().percent
                    self.system_stats["disk_usage"] = psutil.disk_usage('/').percent
                    
                    # Log high resource usage
                    if self.system_stats["cpu_percent"] > 80:
                        logger.warning(f"High CPU usage: {self.system_stats['cpu_percent']:.1f}%")
                    
                    if self.system_stats["memory_percent"] > 80:
                        logger.warning(f"High memory usage: {self.system_stats['memory_percent']:.1f}%")
                    
                    time.sleep(30)  # Update every 30 seconds
                    
                except Exception as e:
                    logger.error(f"Error monitoring system stats: {e}")
                    time.sleep(60)  # Wait longer on error
        
        # Start monitoring in background thread
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        try:
            # Calculate overall statistics
            if self.request_times:
                avg_response_time = sum(self.request_times) / len(self.request_times)
                min_response_time = min(self.request_times)
                max_response_time = max(self.request_times)
                
                # Calculate percentiles
                sorted_times = sorted(self.request_times)
                p50 = sorted_times[int(len(sorted_times) * 0.5)]
                p95 = sorted_times[int(len(sorted_times) * 0.95)]
                p99 = sorted_times[int(len(sorted_times) * 0.99)]
            else:
                avg_response_time = min_response_time = max_response_time = 0
                p50 = p95 = p99 = 0
            
            # Calculate endpoint statistics
            endpoint_stats = {}
            for endpoint, stats in self.endpoint_stats.items():
                if stats["count"] > 0:
                    endpoint_stats[endpoint] = {
                        "count": stats["count"],
                        "avg_time": stats["total_time"] / stats["count"],
                        "min_time": stats["min_time"] if stats["min_time"] != float('inf') else 0,
                        "max_time": stats["max_time"],
                        "error_rate": (stats["errors"] / stats["count"]) * 100,
                        "errors": stats["errors"]
                    }
            
            return {
                "overall": {
                    "total_requests": len(self.request_times),
                    "avg_response_time": avg_response_time,
                    "min_response_time": min_response_time,
                    "max_response_time": max_response_time,
                    "p50_response_time": p50,
                    "p95_response_time": p95,
                    "p99_response_time": p99
                },
                "endpoints": endpoint_stats,
                "system": self.system_stats.copy()
            }
            
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {"error": "Failed to get performance statistics"}

class PerformanceOptimizer:
    """Helper class for performance optimizations"""
    
    @staticmethod
    async def cache_result(key: str, func: Callable, ttl: int = 300):
        """Simple in-memory cache for expensive operations"""
        # TODO: Implement proper caching with Redis or similar
        # This is a placeholder implementation
        return await func()
    
    @staticmethod
    async def batch_database_operations(operations: list, batch_size: int = 100):
        """Batch database operations for better performance"""
        results = []
        for i in range(0, len(operations), batch_size):
            batch = operations[i:i + batch_size]
            # Process batch
            batch_results = await asyncio.gather(*batch, return_exceptions=True)
            results.extend(batch_results)
        return results
    
    @staticmethod
    def optimize_query(query_func: Callable):
        """Decorator for query optimization"""
        async def wrapper(*args, **kwargs):
            # Add query optimization logic here
            # For example: add proper indexes, limit results, etc.
            return await query_func(*args, **kwargs)
        return wrapper

class ResourceMonitor:
    """Monitor system resources and alert on issues"""
    
    def __init__(self):
        self.alerts_sent = set()
    
    def check_resources(self) -> Dict[str, Any]:
        """Check system resources and return status"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            status = {
                "cpu": {
                    "percent": cpu_percent,
                    "status": "critical" if cpu_percent > 90 else "warning" if cpu_percent > 80 else "ok"
                },
                "memory": {
                    "percent": memory.percent,
                    "available_gb": memory.available / (1024**3),
                    "status": "critical" if memory.percent > 90 else "warning" if memory.percent > 80 else "ok"
                },
                "disk": {
                    "percent": disk.percent,
                    "free_gb": disk.free / (1024**3),
                    "status": "critical" if disk.percent > 90 else "warning" if disk.percent > 80 else "ok"
                }
            }
            
            # Send alerts for critical issues
            self._send_alerts(status)
            
            return status
            
        except Exception as e:
            logger.error(f"Error checking system resources: {e}")
            return {"error": "Failed to check system resources"}
    
    def _send_alerts(self, status: Dict[str, Any]):
        """Send alerts for critical resource usage"""
        for resource, data in status.items():
            if data.get("status") == "critical":
                alert_key = f"{resource}_critical"
                if alert_key not in self.alerts_sent:
                    logger.critical(f"Critical {resource} usage: {data.get('percent', 0):.1f}%")
                    self.alerts_sent.add(alert_key)
            elif data.get("status") == "ok" and f"{resource}_critical" in self.alerts_sent:
                # Remove alert when resource usage returns to normal
                self.alerts_sent.discard(f"{resource}_critical")
