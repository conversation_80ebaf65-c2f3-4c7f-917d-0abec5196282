"""
Error handling middleware for comprehensive error management
"""

import traceback
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
import time

class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware for handling errors and exceptions"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            # Add request ID for tracking
            request_id = f"{int(time.time() * 1000)}-{id(request)}"
            request.state.request_id = request_id
            
            # Log incoming request
            logger.info(
                f"Request {request_id}: {request.method} {request.url.path}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "query_params": str(request.query_params),
                    "client_ip": request.client.host if request.client else "unknown"
                }
            )
            
            # Process request
            response = await call_next(request)
            
            # Log successful response
            logger.info(
                f"Response {request_id}: {response.status_code}",
                extra={
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "response_time": getattr(request.state, "response_time", 0)
                }
            )
            
            return response
            
        except HTTPException as e:
            # Handle HTTP exceptions
            logger.warning(
                f"HTTP Exception {request_id}: {e.status_code} - {e.detail}",
                extra={
                    "request_id": request_id,
                    "status_code": e.status_code,
                    "detail": e.detail
                }
            )
            
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "error": {
                        "type": "http_exception",
                        "message": e.detail,
                        "status_code": e.status_code,
                        "request_id": request_id
                    }
                }
            )
            
        except Exception as e:
            # Handle unexpected exceptions
            error_traceback = traceback.format_exc()
            
            logger.error(
                f"Unhandled Exception {request_id}: {str(e)}",
                extra={
                    "request_id": request_id,
                    "error_type": type(e).__name__,
                    "error_message": str(e),
                    "traceback": error_traceback
                }
            )
            
            # Return generic error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "type": "internal_server_error",
                        "message": "An unexpected error occurred",
                        "request_id": request_id
                    }
                }
            )

class DatabaseErrorHandler:
    """Helper class for handling database-specific errors"""
    
    @staticmethod
    def handle_db_error(e: Exception, operation: str = "database operation") -> HTTPException:
        """Convert database errors to appropriate HTTP exceptions"""
        error_message = str(e).lower()
        
        if "unique constraint" in error_message or "duplicate" in error_message:
            return HTTPException(
                status_code=409,
                detail=f"Resource already exists: {operation}"
            )
        elif "foreign key" in error_message:
            return HTTPException(
                status_code=400,
                detail=f"Invalid reference in {operation}"
            )
        elif "not found" in error_message:
            return HTTPException(
                status_code=404,
                detail=f"Resource not found: {operation}"
            )
        elif "connection" in error_message or "timeout" in error_message:
            return HTTPException(
                status_code=503,
                detail=f"Database unavailable: {operation}"
            )
        else:
            logger.error(f"Unhandled database error in {operation}: {e}")
            return HTTPException(
                status_code=500,
                detail=f"Database error in {operation}"
            )

class ValidationErrorHandler:
    """Helper class for handling validation errors"""
    
    @staticmethod
    def handle_validation_error(e: Exception, field: str = None) -> HTTPException:
        """Convert validation errors to appropriate HTTP exceptions"""
        error_message = str(e)
        
        if field:
            detail = f"Validation error in field '{field}': {error_message}"
        else:
            detail = f"Validation error: {error_message}"
        
        return HTTPException(
            status_code=422,
            detail=detail
        )

class BusinessLogicErrorHandler:
    """Helper class for handling business logic errors"""
    
    @staticmethod
    def handle_business_error(message: str, status_code: int = 400) -> HTTPException:
        """Create business logic error"""
        return HTTPException(
            status_code=status_code,
            detail=message
        )
    
    @staticmethod
    def profile_not_active(profile_id: str) -> HTTPException:
        """Profile not active error"""
        return HTTPException(
            status_code=400,
            detail=f"Profile {profile_id} is not active"
        )
    
    @staticmethod
    def campaign_not_ready(campaign_id: str) -> HTTPException:
        """Campaign not ready error"""
        return HTTPException(
            status_code=400,
            detail=f"Campaign {campaign_id} is not ready to start"
        )
    
    @staticmethod
    def browser_not_available(profile_id: str) -> HTTPException:
        """Browser not available error"""
        return HTTPException(
            status_code=503,
            detail=f"Browser not available for profile {profile_id}"
        )
    
    @staticmethod
    def rate_limit_exceeded() -> HTTPException:
        """Rate limit exceeded error"""
        return HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Please try again later."
        )

# Error handling decorators
def handle_db_errors(operation: str = "database operation"):
    """Decorator for handling database errors"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                raise DatabaseErrorHandler.handle_db_error(e, operation)
        return wrapper
    return decorator

def handle_validation_errors(field: str = None):
    """Decorator for handling validation errors"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ValueError as e:
                raise ValidationErrorHandler.handle_validation_error(e, field)
            except TypeError as e:
                raise ValidationErrorHandler.handle_validation_error(e, field)
        return wrapper
    return decorator
