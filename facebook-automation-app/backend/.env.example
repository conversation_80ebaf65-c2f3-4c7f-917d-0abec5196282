# Facebook Automation Backend Configuration

# Server Configuration
HOST=127.0.0.1
PORT=8000
DEBUG=true

# Database Configuration (for future use)
DATABASE_URL=sqlite:///./facebook_automation.db

# Security Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Facebook Configuration (for future use)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Browser Configuration
BROWSER_HEADLESS=false
BROWSER_TIMEOUT=30000
BROWSER_USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36

# Automation Settings
MAX_CONCURRENT_PROFILES=5
MESSAGE_DELAY_MIN=2
MESSAGE_DELAY_MAX=5
SCRAPING_DELAY_MIN=1
SCRAPING_DELAY_MAX=3

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
