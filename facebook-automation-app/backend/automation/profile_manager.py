"""
Profile management for browser automation
"""

import asyncio
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from loguru import logger

from database.connection import SessionLocal
from database.models import Profile
from sqlalchemy.sql import func
from .browser_manager import BrowserManager

class ProfileManager:
    """Manages browser profiles and their lifecycle"""
    
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.active_profiles: Dict[str, Dict[str, Any]] = {}
    
    async def create_profile(self, profile_data: Dict[str, Any]) -> Optional[str]:
        """Create a new browser profile"""
        try:
            db = SessionLocal()
            
            # Create database record
            profile = Profile(**profile_data)
            db.add(profile)
            db.commit()
            db.refresh(profile)
            
            # Create browser profile
            success = await self.browser_manager.create_browser_profile(
                profile.id, 
                profile_data
            )
            
            if success:
                logger.info(f"Created profile: {profile.name} ({profile.id})")
                return profile.id
            else:
                # Rollback database if browser profile creation failed
                db.delete(profile)
                db.commit()
                return None
                
        except Exception as e:
            logger.error(f"Failed to create profile: {e}")
            if 'db' in locals():
                db.rollback()
            return None
        finally:
            if 'db' in locals():
                db.close()
    
    async def activate_profile(self, profile_id: str) -> bool:
        """Activate a profile (launch browser)"""
        try:
            db = SessionLocal()
            
            # Get profile from database
            profile = db.query(Profile).filter(Profile.id == profile_id).first()
            if not profile:
                logger.error(f"Profile not found: {profile_id}")
                return False
            
            # Launch browser
            browser = await self.browser_manager.launch_browser(profile_id)
            if not browser:
                return False
            
            # Update profile status
            profile.status = "active"
            # profile.last_used = func.now()  # Will be set by database default
            db.commit()
            
            # Track active profile
            self.active_profiles[profile_id] = {
                "profile": profile,
                "browser": browser,
                "activated_at": asyncio.get_event_loop().time()
            }
            
            logger.info(f"Activated profile: {profile.name} ({profile_id})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to activate profile {profile_id}: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def deactivate_profile(self, profile_id: str) -> bool:
        """Deactivate a profile (close browser)"""
        try:
            db = SessionLocal()
            
            # Close browser
            success = await self.browser_manager.close_browser(profile_id)
            
            # Update profile status
            profile = db.query(Profile).filter(Profile.id == profile_id).first()
            if profile:
                profile.status = "inactive"
                db.commit()
            
            # Remove from active profiles
            if profile_id in self.active_profiles:
                del self.active_profiles[profile_id]
            
            logger.info(f"Deactivated profile: {profile_id}")
            return success
            
        except Exception as e:
            logger.error(f"Failed to deactivate profile {profile_id}: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def get_active_profiles(self) -> List[Dict[str, Any]]:
        """Get list of active profiles"""
        return [
            {
                "profile_id": profile_id,
                "profile_name": data["profile"].name,
                "activated_at": data["activated_at"]
            }
            for profile_id, data in self.active_profiles.items()
        ]
    
    async def is_profile_active(self, profile_id: str) -> bool:
        """Check if profile is active"""
        return profile_id in self.active_profiles
    
    async def get_browser_for_profile(self, profile_id: str) -> Optional[Any]:
        """Get browser instance for profile"""
        if profile_id not in self.active_profiles:
            return None
        return self.active_profiles[profile_id]["browser"]
    
    async def update_profile(self, profile_id: str, updates: Dict[str, Any]) -> bool:
        """Update profile configuration"""
        try:
            db = SessionLocal()
            
            # Update database
            profile = db.query(Profile).filter(Profile.id == profile_id).first()
            if not profile:
                logger.error(f"Profile not found: {profile_id}")
                return False
            
            for key, value in updates.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            
            db.commit()
            
            # Update browser configuration if profile is active
            if profile_id in self.active_profiles:
                await self.browser_manager.update_profile_config(profile_id, updates)
            
            logger.info(f"Updated profile: {profile_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update profile {profile_id}: {e}")
            if 'db' in locals():
                db.rollback()
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def delete_profile(self, profile_id: str) -> bool:
        """Delete a profile"""
        try:
            db = SessionLocal()
            
            # Deactivate if active
            if profile_id in self.active_profiles:
                await self.deactivate_profile(profile_id)
            
            # Delete browser profile
            await self.browser_manager.delete_profile(profile_id)
            
            # Delete from database
            profile = db.query(Profile).filter(Profile.id == profile_id).first()
            if profile:
                db.delete(profile)
                db.commit()
            
            logger.info(f"Deleted profile: {profile_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete profile {profile_id}: {e}")
            if 'db' in locals():
                db.rollback()
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def cleanup_inactive_profiles(self, max_inactive_time: int = 3600):
        """Cleanup profiles that have been inactive for too long"""
        try:
            current_time = asyncio.get_event_loop().time()
            inactive_profiles = []
            
            for profile_id, data in self.active_profiles.items():
                if current_time - data["activated_at"] > max_inactive_time:
                    inactive_profiles.append(profile_id)
            
            for profile_id in inactive_profiles:
                await self.deactivate_profile(profile_id)
                logger.info(f"Cleaned up inactive profile: {profile_id}")
            
        except Exception as e:
            logger.error(f"Failed to cleanup inactive profiles: {e}")
    
    async def shutdown(self):
        """Shutdown profile manager and close all browsers"""
        try:
            # Deactivate all profiles
            for profile_id in list(self.active_profiles.keys()):
                await self.deactivate_profile(profile_id)
            
            # Close all browsers
            await self.browser_manager.close_all_browsers()
            
            logger.info("Profile manager shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during profile manager shutdown: {e}")
