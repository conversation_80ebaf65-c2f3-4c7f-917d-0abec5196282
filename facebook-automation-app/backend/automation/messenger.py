"""
Facebook messaging functionality using zendriver
"""

import asyncio
import random
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from database.connection import SessionLocal
from database.models import Message, Profile, Campaign
from .browser_manager import BrowserManager

class FacebookMessenger:
    """Facebook messenger for sending bulk messages"""
    
    def __init__(self, browser_manager: BrowserManager):
        self.browser_manager = browser_manager
        self.active_campaigns: Dict[str, Dict[str, Any]] = {}
        self.message_queue: List[str] = []  # message IDs to send
    
    async def start_messaging_campaign(self, campaign_id: str) -> bool:
        """Start a messaging campaign"""
        try:
            db = SessionLocal()
            
            # Get campaign from database
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if not campaign:
                logger.error(f"Campaign not found: {campaign_id}")
                return False
            
            # Get browser for profile
            browser = await self.browser_manager.get_browser(campaign.profile_id)
            if not browser:
                logger.error(f"No browser available for profile: {campaign.profile_id}")
                return False
            
            # Get pending messages for campaign
            pending_messages = db.query(Message).filter(
                Message.campaign_id == campaign_id,
                Message.status == "pending"
            ).all()
            
            if not pending_messages:
                logger.warning(f"No pending messages for campaign: {campaign_id}")
                return False
            
            # Update campaign status
            campaign.status = "active"
            campaign.started_at = datetime.now()
            db.commit()
            
            # Track active campaign
            self.active_campaigns[campaign_id] = {
                "campaign": campaign,
                "browser": browser,
                "pending_messages": [msg.id for msg in pending_messages],
                "sent_count": 0,
                "failed_count": 0,
                "started_at": datetime.now()
            }
            
            # Start messaging in background
            asyncio.create_task(self._process_campaign_messages(campaign_id))
            
            logger.info(f"Started messaging campaign: {campaign.name} ({campaign_id})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start messaging campaign {campaign_id}: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def stop_messaging_campaign(self, campaign_id: str) -> bool:
        """Stop a messaging campaign"""
        try:
            if campaign_id not in self.active_campaigns:
                logger.warning(f"Messaging campaign not active: {campaign_id}")
                return True
            
            # Mark campaign as stopped
            campaign_data = self.active_campaigns[campaign_id]
            campaign_data["stopped"] = True
            
            # Update database
            db = SessionLocal()
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if campaign:
                campaign.status = "paused"
                campaign.successful_actions = campaign_data["sent_count"]
                campaign.failed_actions = campaign_data["failed_count"]
                db.commit()
            
            logger.info(f"Stopped messaging campaign: {campaign_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop messaging campaign {campaign_id}: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def send_single_message(self, message_id: str) -> bool:
        """Send a single message"""
        try:
            db = SessionLocal()
            
            # Get message from database
            message = db.query(Message).filter(Message.id == message_id).first()
            if not message:
                logger.error(f"Message not found: {message_id}")
                return False
            
            # Get browser for profile
            browser = await self.browser_manager.get_browser(message.profile_id)
            if not browser:
                logger.error(f"No browser available for profile: {message.profile_id}")
                return False
            
            # Send message
            success = await self._send_message_to_target(message, browser)
            
            if success:
                message.status = "sent"
                message.sent_at = datetime.now()
                logger.info(f"Sent message to {message.target_uid}")
            else:
                message.status = "failed"
                message.error_message = "Failed to send message"
                message.retry_count += 1
                logger.error(f"Failed to send message to {message.target_uid}")
            
            db.commit()
            return success
            
        except Exception as e:
            logger.error(f"Error sending message {message_id}: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def _process_campaign_messages(self, campaign_id: str):
        """Process all messages in a campaign"""
        try:
            campaign_data = self.active_campaigns[campaign_id]
            campaign = campaign_data["campaign"]
            browser = campaign_data["browser"]
            
            logger.info(f"Processing messages for campaign: {campaign.name}")
            
            # Get message delay settings
            min_delay = int(os.getenv("MESSAGE_DELAY_MIN", "2"))
            max_delay = int(os.getenv("MESSAGE_DELAY_MAX", "5"))
            
            for message_id in campaign_data["pending_messages"]:
                if campaign_data.get("stopped"):
                    break
                
                # Send message
                success = await self._send_message_by_id(message_id, browser)
                
                if success:
                    campaign_data["sent_count"] += 1
                else:
                    campaign_data["failed_count"] += 1
                
                # Update campaign progress
                await self._update_campaign_progress(campaign_id)
                
                # Random delay between messages
                delay = random.uniform(min_delay, max_delay)
                await asyncio.sleep(delay)
            
            # Mark campaign as completed
            await self._complete_campaign(campaign_id)
            
        except Exception as e:
            logger.error(f"Error processing campaign messages {campaign_id}: {e}")
            await self._fail_campaign(campaign_id, str(e))
    
    async def _send_message_by_id(self, message_id: str, browser: Any) -> bool:
        """Send a message by ID"""
        try:
            db = SessionLocal()
            
            message = db.query(Message).filter(Message.id == message_id).first()
            if not message:
                return False
            
            success = await self._send_message_to_target(message, browser)
            
            if success:
                message.status = "sent"
                message.sent_at = datetime.now()
            else:
                message.status = "failed"
                message.error_message = "Failed to send message"
                message.retry_count += 1
            
            db.commit()
            return success
            
        except Exception as e:
            logger.error(f"Error sending message {message_id}: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    async def _send_message_to_target(self, message: Message, browser: Any) -> bool:
        """Send message to specific target using browser"""
        try:
            # TODO: Replace with actual zendriver implementation
            # This is a placeholder implementation
            
            # Navigate to Facebook messages
            # page = await browser.new_page()
            # await page.goto("https://www.facebook.com/messages")
            
            # Search for target user
            # await page.fill('[placeholder="Search Messenger"]', message.target_uid)
            # await page.press('[placeholder="Search Messenger"]', 'Enter')
            
            # Wait for search results and click on target
            # await page.wait_for_selector(f'[data-testid="search-result-{message.target_uid}"]')
            # await page.click(f'[data-testid="search-result-{message.target_uid}"]')
            
            # Type and send message
            # await page.fill('[data-testid="message-input"]', message.content)
            # await page.press('[data-testid="message-input"]', 'Enter')
            
            # Simulate successful send
            await asyncio.sleep(1)  # Simulate network delay
            
            logger.info(f"Sent message to {message.target_uid}: {message.content[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to {message.target_uid}: {e}")
            return False
    
    async def _update_campaign_progress(self, campaign_id: str):
        """Update campaign progress in database"""
        try:
            if campaign_id not in self.active_campaigns:
                return
            
            campaign_data = self.active_campaigns[campaign_id]
            total_messages = len(campaign_data["pending_messages"])
            completed_messages = campaign_data["sent_count"] + campaign_data["failed_count"]
            progress = (completed_messages / total_messages) * 100 if total_messages > 0 else 0
            
            db = SessionLocal()
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if campaign:
                campaign.progress = progress
                campaign.successful_actions = campaign_data["sent_count"]
                campaign.failed_actions = campaign_data["failed_count"]
                campaign.completed_targets = completed_messages
                db.commit()
            
        except Exception as e:
            logger.error(f"Failed to update campaign progress {campaign_id}: {e}")
        finally:
            if 'db' in locals():
                db.close()
    
    async def _complete_campaign(self, campaign_id: str):
        """Mark campaign as completed"""
        try:
            campaign_data = self.active_campaigns[campaign_id]
            
            db = SessionLocal()
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if campaign:
                campaign.status = "completed"
                campaign.completed_at = datetime.now()
                campaign.progress = 100.0
                campaign.successful_actions = campaign_data["sent_count"]
                campaign.failed_actions = campaign_data["failed_count"]
                db.commit()
            
            # Remove from active campaigns
            del self.active_campaigns[campaign_id]
            
            logger.info(f"Completed messaging campaign: {campaign_id}")
            
        except Exception as e:
            logger.error(f"Failed to complete campaign {campaign_id}: {e}")
        finally:
            if 'db' in locals():
                db.close()
    
    async def _fail_campaign(self, campaign_id: str, error_message: str):
        """Mark campaign as failed"""
        try:
            db = SessionLocal()
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if campaign:
                campaign.status = "failed"
                # Store error in campaign description or add error field
                db.commit()
            
            # Remove from active campaigns
            if campaign_id in self.active_campaigns:
                del self.active_campaigns[campaign_id]
            
            logger.error(f"Failed messaging campaign: {campaign_id} - {error_message}")
            
        except Exception as e:
            logger.error(f"Failed to mark campaign as failed {campaign_id}: {e}")
        finally:
            if 'db' in locals():
                db.close()
    
    async def get_campaign_status(self, campaign_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a messaging campaign"""
        if campaign_id not in self.active_campaigns:
            return None
        
        campaign_data = self.active_campaigns[campaign_id]
        total_messages = len(campaign_data["pending_messages"])
        completed_messages = campaign_data["sent_count"] + campaign_data["failed_count"]
        progress = (completed_messages / total_messages) * 100 if total_messages > 0 else 0
        
        return {
            "campaign_id": campaign_id,
            "status": "running",
            "progress": progress,
            "sent_count": campaign_data["sent_count"],
            "failed_count": campaign_data["failed_count"],
            "total_messages": total_messages,
            "started_at": campaign_data["started_at"]
        }
    
    async def get_active_campaigns(self) -> List[Dict[str, Any]]:
        """Get list of active messaging campaigns"""
        return [
            await self.get_campaign_status(campaign_id)
            for campaign_id in self.active_campaigns.keys()
        ]
