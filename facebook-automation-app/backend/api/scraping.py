"""
Scraping management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from loguru import logger

from database.connection import get_db
from database.models import ScrapingSession, Profile, Campaign
from sqlalchemy.sql import func
from schemas.scraping import ScrapingSessionCreate, ScrapingSessionUpdate, ScrapingSessionResponse, ScrapingSessionList

router = APIRouter(prefix="/api/scraping", tags=["scraping"])

@router.get("/sessions", response_model=ScrapingSessionList)
async def get_scraping_sessions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    profile_id: Optional[str] = Query(None),
    campaign_id: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all scraping sessions with pagination and filtering"""
    try:
        query = db.query(ScrapingSession)
        
        if status:
            query = query.filter(ScrapingSession.status == status)
        if profile_id:
            query = query.filter(ScrapingSession.profile_id == profile_id)
        if campaign_id:
            query = query.filter(ScrapingSession.campaign_id == campaign_id)
        
        total = query.count()
        sessions = query.offset(skip).limit(limit).all()
        
        return ScrapingSessionList(
            sessions=sessions,
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
    except Exception as e:
        logger.error(f"Error fetching scraping sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping sessions")

@router.get("/sessions/{session_id}", response_model=ScrapingSessionResponse)
async def get_scraping_session(session_id: str, db: Session = Depends(get_db)):
    """Get a specific scraping session by ID"""
    try:
        session = db.query(ScrapingSession).filter(ScrapingSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching scraping session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping session")

@router.post("/sessions", response_model=ScrapingSessionResponse)
async def create_scraping_session(session_data: ScrapingSessionCreate, db: Session = Depends(get_db)):
    """Create a new scraping session"""
    try:
        # Verify profile exists
        profile = db.query(Profile).filter(Profile.id == session_data.profile_id).first()
        if not profile:
            raise HTTPException(status_code=400, detail="Profile not found")
        
        # Verify campaign exists if provided
        if session_data.campaign_id:
            campaign = db.query(Campaign).filter(Campaign.id == session_data.campaign_id).first()
            if not campaign:
                raise HTTPException(status_code=400, detail="Campaign not found")
        
        # Create new scraping session
        session = ScrapingSession(**session_data.dict())
        db.add(session)
        db.commit()
        db.refresh(session)
        
        logger.info(f"Created new scraping session: {session.name} ({session.id})")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating scraping session: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create scraping session")

@router.put("/sessions/{session_id}", response_model=ScrapingSessionResponse)
async def update_scraping_session(
    session_id: str, 
    session_data: ScrapingSessionUpdate, 
    db: Session = Depends(get_db)
):
    """Update an existing scraping session"""
    try:
        session = db.query(ScrapingSession).filter(ScrapingSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")
        
        # Update session fields
        update_data = session_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(session, field, value)
        
        db.commit()
        db.refresh(session)
        
        logger.info(f"Updated scraping session: {session.name} ({session.id})")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating scraping session {session_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update scraping session")

@router.delete("/sessions/{session_id}")
async def delete_scraping_session(session_id: str, db: Session = Depends(get_db)):
    """Delete a scraping session"""
    try:
        session = db.query(ScrapingSession).filter(ScrapingSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")
        
        # Check if session is running
        if session.status == "running":
            raise HTTPException(status_code=400, detail="Cannot delete running scraping session")
        
        db.delete(session)
        db.commit()
        
        logger.info(f"Deleted scraping session: {session.name} ({session.id})")
        return {"message": "Scraping session deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting scraping session {session_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete scraping session")

@router.post("/sessions/{session_id}/start")
async def start_scraping_session(session_id: str, db: Session = Depends(get_db)):
    """Start a scraping session"""
    try:
        session = db.query(ScrapingSession).filter(ScrapingSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")
        
        if session.status not in ["pending", "paused"]:
            raise HTTPException(status_code=400, detail="Scraping session cannot be started")
        
        session.status = "running"
        session.started_at = func.now()
        db.commit()
        
        # TODO: Start actual scraping process
        
        logger.info(f"Started scraping session: {session.name} ({session.id})")
        return {"message": "Scraping session started successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting scraping session {session_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to start scraping session")

@router.post("/sessions/{session_id}/stop")
async def stop_scraping_session(session_id: str, db: Session = Depends(get_db)):
    """Stop a scraping session"""
    try:
        session = db.query(ScrapingSession).filter(ScrapingSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Scraping session not found")
        
        if session.status != "running":
            raise HTTPException(status_code=400, detail="Scraping session is not running")
        
        session.status = "paused"
        db.commit()
        
        # TODO: Stop actual scraping process
        
        logger.info(f"Stopped scraping session: {session.name} ({session.id})")
        return {"message": "Scraping session stopped successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping scraping session {session_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to stop scraping session")
