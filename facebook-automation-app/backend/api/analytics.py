"""
Analytics API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from datetime import datetime, timedelta
from loguru import logger

from database.connection import get_db
from database.models import Analytics, Profile, Campaign, Message, ScrapingSession
from schemas.analytics import AnalyticsResponse, DashboardStats, PerformanceMetrics, AnalyticsChart, TimeSeriesData

router = APIRouter(prefix="/api/analytics", tags=["analytics"])

@router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    days: int = Query(7, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get dashboard statistics"""
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Profile stats
        total_profiles = db.query(Profile).count()
        active_profiles = db.query(Profile).filter(Profile.status == "active").count()
        
        # Campaign stats
        total_campaigns = db.query(Campaign).count()
        active_campaigns = db.query(Campaign).filter(Campaign.status == "active").count()
        
        # Message stats
        total_messages_sent = db.query(Message).filter(Message.status.in_(["sent", "delivered"])).count()
        today_messages = db.query(Message).filter(
            Message.created_at >= start_date,
            Message.status.in_(["sent", "delivered"])
        ).count()
        
        # Scraping stats
        total_uids_scraped = db.query(func.sum(ScrapingSession.unique_uids)).scalar() or 0
        today_scraped = db.query(func.sum(ScrapingSession.unique_uids)).filter(
            ScrapingSession.created_at >= start_date
        ).scalar() or 0
        
        # Success rate calculation
        total_attempts = db.query(Message).count()
        successful_messages = db.query(Message).filter(Message.status.in_(["sent", "delivered"])).count()
        success_rate = (successful_messages / total_attempts * 100) if total_attempts > 0 else 0
        
        return DashboardStats(
            total_profiles=total_profiles,
            active_profiles=active_profiles,
            total_campaigns=total_campaigns,
            active_campaigns=active_campaigns,
            total_messages_sent=total_messages_sent,
            total_uids_scraped=int(total_uids_scraped),
            success_rate=round(success_rate, 2),
            today_messages=today_messages,
            today_scraped=int(today_scraped)
        )
        
    except Exception as e:
        logger.error(f"Error fetching dashboard stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch dashboard statistics")

@router.get("/performance", response_model=PerformanceMetrics)
async def get_performance_metrics(
    days: int = Query(7, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get performance metrics"""
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        hours_in_period = days * 24
        
        # Messages per hour
        messages_in_period = db.query(Message).filter(
            Message.created_at >= start_date,
            Message.status.in_(["sent", "delivered"])
        ).count()
        messages_per_hour = messages_in_period / hours_in_period if hours_in_period > 0 else 0
        
        # Scraping rate (UIDs per hour)
        uids_in_period = db.query(func.sum(ScrapingSession.unique_uids)).filter(
            ScrapingSession.created_at >= start_date
        ).scalar() or 0
        scraping_rate = uids_in_period / hours_in_period if hours_in_period > 0 else 0
        
        # Error rate
        total_messages = db.query(Message).filter(Message.created_at >= start_date).count()
        failed_messages = db.query(Message).filter(
            Message.created_at >= start_date,
            Message.status.in_(["failed", "blocked"])
        ).count()
        error_rate = (failed_messages / total_messages * 100) if total_messages > 0 else 0
        
        # Uptime percentage (simplified - based on successful operations)
        total_operations = total_messages + db.query(ScrapingSession).filter(
            ScrapingSession.created_at >= start_date
        ).count()
        successful_operations = messages_in_period + db.query(ScrapingSession).filter(
            ScrapingSession.created_at >= start_date,
            ScrapingSession.status == "completed"
        ).count()
        uptime_percentage = (successful_operations / total_operations * 100) if total_operations > 0 else 100
        
        return PerformanceMetrics(
            messages_per_hour=round(messages_per_hour, 2),
            scraping_rate=round(scraping_rate, 2),
            error_rate=round(error_rate, 2),
            uptime_percentage=round(uptime_percentage, 2)
        )
        
    except Exception as e:
        logger.error(f"Error fetching performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch performance metrics")

@router.get("/charts/messages", response_model=AnalyticsChart)
async def get_messages_chart(
    days: int = Query(7, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get messages chart data"""
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Query messages by day
        results = db.query(
            func.date(Message.created_at).label('date'),
            func.count(Message.id).label('count')
        ).filter(
            Message.created_at >= start_date,
            Message.status.in_(["sent", "delivered"])
        ).group_by(func.date(Message.created_at)).all()
        
        # Convert to time series data
        data = [
            TimeSeriesData(date=datetime.combine(result.date, datetime.min.time()), value=float(result.count))
            for result in results
        ]
        
        return AnalyticsChart(
            title="Messages Sent Over Time",
            data=data,
            metric_type="count"
        )
        
    except Exception as e:
        logger.error(f"Error fetching messages chart: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch messages chart data")

@router.get("/charts/scraping", response_model=AnalyticsChart)
async def get_scraping_chart(
    days: int = Query(7, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get scraping chart data"""
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Query scraping by day
        results = db.query(
            func.date(ScrapingSession.created_at).label('date'),
            func.sum(ScrapingSession.unique_uids).label('total_uids')
        ).filter(
            ScrapingSession.created_at >= start_date,
            ScrapingSession.status == "completed"
        ).group_by(func.date(ScrapingSession.created_at)).all()
        
        # Convert to time series data
        data = [
            TimeSeriesData(
                date=datetime.combine(result.date, datetime.min.time()), 
                value=float(result.total_uids or 0)
            )
            for result in results
        ]
        
        return AnalyticsChart(
            title="UIDs Scraped Over Time",
            data=data,
            metric_type="count"
        )
        
    except Exception as e:
        logger.error(f"Error fetching scraping chart: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping chart data")

@router.post("/record")
async def record_analytics(
    metric_name: str,
    metric_value: float,
    metric_type: str = "count",
    profile_id: Optional[str] = None,
    campaign_id: Optional[str] = None,
    metadata: Optional[dict] = None,
    db: Session = Depends(get_db)
):
    """Record a custom analytics metric"""
    try:
        analytics = Analytics(
            metric_name=metric_name,
            metric_value=metric_value,
            metric_type=metric_type,
            profile_id=profile_id,
            campaign_id=campaign_id,
            metadata=metadata
        )
        
        db.add(analytics)
        db.commit()
        
        logger.info(f"Recorded analytics metric: {metric_name} = {metric_value}")
        return {"message": "Analytics metric recorded successfully"}
        
    except Exception as e:
        logger.error(f"Error recording analytics: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to record analytics metric")
