"""
Messaging API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from loguru import logger

from database.connection import get_db
from database.models import Message, Profile, Campaign
from schemas.message import MessageC<PERSON>, MessageResponse, MessageList, MessageStats

router = APIRouter(prefix="/api/messaging", tags=["messaging"])

@router.get("/messages", response_model=MessageList)
async def get_messages(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    profile_id: Optional[str] = Query(None),
    campaign_id: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all messages with pagination and filtering"""
    try:
        query = db.query(Message)
        
        if status:
            query = query.filter(Message.status == status)
        if profile_id:
            query = query.filter(Message.profile_id == profile_id)
        if campaign_id:
            query = query.filter(Message.campaign_id == campaign_id)
        
        total = query.count()
        messages = query.offset(skip).limit(limit).all()
        
        return MessageList(
            messages=messages,
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
    except Exception as e:
        logger.error(f"Error fetching messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch messages")

@router.get("/stats", response_model=MessageStats)
async def get_message_stats(db: Session = Depends(get_db)):
    """Get message statistics"""
    try:
        total_messages = db.query(Message).count()
        sent_messages = db.query(Message).filter(Message.status == "sent").count()
        delivered_messages = db.query(Message).filter(Message.status == "delivered").count()
        failed_messages = db.query(Message).filter(Message.status == "failed").count()
        pending_messages = db.query(Message).filter(Message.status == "pending").count()
        
        return MessageStats(
            total_messages=total_messages,
            sent_messages=sent_messages,
            delivered_messages=delivered_messages,
            failed_messages=failed_messages,
            pending_messages=pending_messages
        )
    except Exception as e:
        logger.error(f"Error fetching message stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch message statistics")

@router.get("/messages/{message_id}", response_model=MessageResponse)
async def get_message(message_id: str, db: Session = Depends(get_db)):
    """Get a specific message by ID"""
    try:
        message = db.query(Message).filter(Message.id == message_id).first()
        if not message:
            raise HTTPException(status_code=404, detail="Message not found")
        return message
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching message {message_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch message")

@router.post("/messages", response_model=MessageResponse)
async def create_message(message_data: MessageCreate, db: Session = Depends(get_db)):
    """Create a new message"""
    try:
        # Verify profile exists
        profile = db.query(Profile).filter(Profile.id == message_data.profile_id).first()
        if not profile:
            raise HTTPException(status_code=400, detail="Profile not found")
        
        # Verify campaign exists if provided
        if message_data.campaign_id:
            campaign = db.query(Campaign).filter(Campaign.id == message_data.campaign_id).first()
            if not campaign:
                raise HTTPException(status_code=400, detail="Campaign not found")
        
        # Create new message
        message = Message(**message_data.dict())
        db.add(message)
        db.commit()
        db.refresh(message)
        
        logger.info(f"Created new message for target {message.target_uid} ({message.id})")
        return message
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating message: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create message")

@router.post("/send-bulk")
async def send_bulk_messages(
    profile_id: str,
    campaign_id: Optional[str] = None,
    target_uids: List[str] = [],
    message_content: str = "",
    message_type: str = "text",
    db: Session = Depends(get_db)
):
    """Send bulk messages to multiple targets"""
    try:
        # Verify profile exists
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=400, detail="Profile not found")
        
        # Verify campaign exists if provided
        if campaign_id:
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if not campaign:
                raise HTTPException(status_code=400, detail="Campaign not found")
        
        if not target_uids:
            raise HTTPException(status_code=400, detail="No target UIDs provided")
        
        if not message_content:
            raise HTTPException(status_code=400, detail="Message content is required")
        
        # Create messages for all targets
        messages = []
        for target_uid in target_uids:
            message = Message(
                content=message_content,
                message_type=message_type,
                target_uid=target_uid,
                profile_id=profile_id,
                campaign_id=campaign_id
            )
            messages.append(message)
        
        db.add_all(messages)
        db.commit()
        
        # TODO: Queue messages for sending
        
        logger.info(f"Created {len(messages)} bulk messages for profile {profile_id}")
        return {
            "message": f"Created {len(messages)} messages for bulk sending",
            "total_messages": len(messages)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating bulk messages: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create bulk messages")

@router.post("/messages/{message_id}/retry")
async def retry_message(message_id: str, db: Session = Depends(get_db)):
    """Retry sending a failed message"""
    try:
        message = db.query(Message).filter(Message.id == message_id).first()
        if not message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        if message.status not in ["failed", "blocked"]:
            raise HTTPException(status_code=400, detail="Message cannot be retried")
        
        message.status = "pending"
        message.retry_count += 1
        message.error_message = None
        db.commit()
        
        # TODO: Queue message for retry
        
        logger.info(f"Queued message {message_id} for retry")
        return {"message": "Message queued for retry"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying message {message_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to retry message")
