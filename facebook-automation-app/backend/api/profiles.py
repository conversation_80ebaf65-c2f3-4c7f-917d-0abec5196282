"""
Profile management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from loguru import logger

from database.connection import get_db
from database.models import Profile
from schemas.profile import ProfileCreate, ProfileUpdate, ProfileResponse, ProfileList, ProfileStats

router = APIRouter(prefix="/api/profiles", tags=["profiles"])

@router.get("/", response_model=ProfileList)
async def get_profiles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all profiles with pagination and filtering"""
    try:
        query = db.query(Profile)
        
        if status:
            query = query.filter(Profile.status == status)
        
        total = query.count()
        profiles = query.offset(skip).limit(limit).all()
        
        return ProfileList(
            profiles=profiles,
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
    except Exception as e:
        logger.error(f"Error fetching profiles: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profiles")

@router.get("/stats", response_model=ProfileStats)
async def get_profile_stats(db: Session = Depends(get_db)):
    """Get profile statistics"""
    try:
        total_profiles = db.query(Profile).count()
        active_profiles = db.query(Profile).filter(Profile.status == "active").count()
        inactive_profiles = db.query(Profile).filter(Profile.status == "inactive").count()
        banned_profiles = db.query(Profile).filter(Profile.status == "banned").count()
        error_profiles = db.query(Profile).filter(Profile.status == "error").count()
        
        return ProfileStats(
            total_profiles=total_profiles,
            active_profiles=active_profiles,
            inactive_profiles=inactive_profiles,
            banned_profiles=banned_profiles,
            error_profiles=error_profiles
        )
    except Exception as e:
        logger.error(f"Error fetching profile stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile statistics")

@router.get("/{profile_id}", response_model=ProfileResponse)
async def get_profile(profile_id: str, db: Session = Depends(get_db)):
    """Get a specific profile by ID"""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        return profile
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile")

@router.post("/", response_model=ProfileResponse)
async def create_profile(profile_data: ProfileCreate, db: Session = Depends(get_db)):
    """Create a new profile"""
    try:
        # Check if profile with same name exists
        existing = db.query(Profile).filter(Profile.name == profile_data.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="Profile with this name already exists")
        
        # Create new profile
        profile = Profile(**profile_data.dict())
        db.add(profile)
        db.commit()
        db.refresh(profile)
        
        logger.info(f"Created new profile: {profile.name} ({profile.id})")
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating profile: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create profile")

@router.put("/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: str, 
    profile_data: ProfileUpdate, 
    db: Session = Depends(get_db)
):
    """Update an existing profile"""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        # Update profile fields
        update_data = profile_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(profile, field, value)
        
        db.commit()
        db.refresh(profile)
        
        logger.info(f"Updated profile: {profile.name} ({profile.id})")
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating profile {profile_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update profile")

@router.delete("/{profile_id}")
async def delete_profile(profile_id: str, db: Session = Depends(get_db)):
    """Delete a profile"""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        # Check if profile is being used in active campaigns
        # TODO: Add check for active campaigns
        
        db.delete(profile)
        db.commit()
        
        logger.info(f"Deleted profile: {profile.name} ({profile.id})")
        return {"message": "Profile deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting profile {profile_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete profile")
