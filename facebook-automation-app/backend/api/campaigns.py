"""
Campaign management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from loguru import logger

from database.connection import get_db
from database.models import Campaign, Profile
from sqlalchemy.sql import func
from schemas.campaign import CampaignCreate, CampaignUpdate, CampaignResponse, CampaignList, CampaignStats

router = APIRouter(prefix="/api/campaigns", tags=["campaigns"])

@router.get("/", response_model=CampaignList)
async def get_campaigns(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    profile_id: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all campaigns with pagination and filtering"""
    try:
        query = db.query(Campaign)
        
        if status:
            query = query.filter(Campaign.status == status)
        if profile_id:
            query = query.filter(Campaign.profile_id == profile_id)
        
        total = query.count()
        campaigns = query.offset(skip).limit(limit).all()
        
        return CampaignList(
            campaigns=campaigns,
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
    except Exception as e:
        logger.error(f"Error fetching campaigns: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch campaigns")

@router.get("/stats", response_model=CampaignStats)
async def get_campaign_stats(db: Session = Depends(get_db)):
    """Get campaign statistics"""
    try:
        total_campaigns = db.query(Campaign).count()
        active_campaigns = db.query(Campaign).filter(Campaign.status == "active").count()
        completed_campaigns = db.query(Campaign).filter(Campaign.status == "completed").count()
        failed_campaigns = db.query(Campaign).filter(Campaign.status == "failed").count()
        draft_campaigns = db.query(Campaign).filter(Campaign.status == "draft").count()
        
        return CampaignStats(
            total_campaigns=total_campaigns,
            active_campaigns=active_campaigns,
            completed_campaigns=completed_campaigns,
            failed_campaigns=failed_campaigns,
            draft_campaigns=draft_campaigns
        )
    except Exception as e:
        logger.error(f"Error fetching campaign stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch campaign statistics")

@router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(campaign_id: str, db: Session = Depends(get_db)):
    """Get a specific campaign by ID"""
    try:
        campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        return campaign
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching campaign {campaign_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch campaign")

@router.post("/", response_model=CampaignResponse)
async def create_campaign(campaign_data: CampaignCreate, db: Session = Depends(get_db)):
    """Create a new campaign"""
    try:
        # Verify profile exists
        profile = db.query(Profile).filter(Profile.id == campaign_data.profile_id).first()
        if not profile:
            raise HTTPException(status_code=400, detail="Profile not found")
        
        # Check if campaign with same name exists for this profile
        existing = db.query(Campaign).filter(
            Campaign.name == campaign_data.name,
            Campaign.profile_id == campaign_data.profile_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="Campaign with this name already exists for this profile")
        
        # Create new campaign
        campaign = Campaign(**campaign_data.dict())
        db.add(campaign)
        db.commit()
        db.refresh(campaign)
        
        logger.info(f"Created new campaign: {campaign.name} ({campaign.id})")
        return campaign
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating campaign: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create campaign")

@router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: str, 
    campaign_data: CampaignUpdate, 
    db: Session = Depends(get_db)
):
    """Update an existing campaign"""
    try:
        campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Update campaign fields
        update_data = campaign_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(campaign, field, value)
        
        db.commit()
        db.refresh(campaign)
        
        logger.info(f"Updated campaign: {campaign.name} ({campaign.id})")
        return campaign
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating campaign {campaign_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update campaign")

@router.delete("/{campaign_id}")
async def delete_campaign(campaign_id: str, db: Session = Depends(get_db)):
    """Delete a campaign"""
    try:
        campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Check if campaign is active
        if campaign.status == "active":
            raise HTTPException(status_code=400, detail="Cannot delete active campaign")
        
        db.delete(campaign)
        db.commit()
        
        logger.info(f"Deleted campaign: {campaign.name} ({campaign.id})")
        return {"message": "Campaign deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting campaign {campaign_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete campaign")

@router.post("/{campaign_id}/start")
async def start_campaign(campaign_id: str, db: Session = Depends(get_db)):
    """Start a campaign"""
    try:
        campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        if campaign.status not in ["draft", "paused"]:
            raise HTTPException(status_code=400, detail="Campaign cannot be started")
        
        campaign.status = "active"
        campaign.started_at = func.now()
        db.commit()
        
        # TODO: Start actual campaign execution
        
        logger.info(f"Started campaign: {campaign.name} ({campaign.id})")
        return {"message": "Campaign started successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting campaign {campaign_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to start campaign")

@router.post("/{campaign_id}/pause")
async def pause_campaign(campaign_id: str, db: Session = Depends(get_db)):
    """Pause a campaign"""
    try:
        campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        if campaign.status != "active":
            raise HTTPException(status_code=400, detail="Campaign is not active")
        
        campaign.status = "paused"
        db.commit()
        
        # TODO: Pause actual campaign execution
        
        logger.info(f"Paused campaign: {campaign.name} ({campaign.id})")
        return {"message": "Campaign paused successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pausing campaign {campaign_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to pause campaign")
