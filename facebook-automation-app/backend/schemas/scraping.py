"""
Scraping session schemas for API requests and responses
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class ScrapingSessionBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    target_url: str = Field(..., description="Facebook page/group URL to scrape")
    target_type: str = Field(..., description="Type: page, group, post, search")
    scraping_method: str = Field(..., description="Method: comments, likes, shares, members")
    max_results: int = Field(1000, ge=1, le=10000)

class ScrapingSessionCreate(ScrapingSessionBase):
    profile_id: str
    campaign_id: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None

class ScrapingSessionUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    target_url: Optional[str] = None
    target_type: Optional[str] = None
    scraping_method: Optional[str] = None
    max_results: Optional[int] = Field(None, ge=1, le=10000)
    filters: Optional[Dict[str, Any]] = None
    status: Optional[str] = None

class ScrapingSessionResponse(ScrapingSessionBase):
    id: str
    profile_id: str
    campaign_id: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None
    status: str
    progress: float
    total_found: int
    total_scraped: int
    unique_uids: int
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int
    
    class Config:
        from_attributes = True

class ScrapingSessionList(BaseModel):
    sessions: List[ScrapingSessionResponse]
    total: int
    page: int
    per_page: int
