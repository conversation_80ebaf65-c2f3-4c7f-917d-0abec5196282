"""
Analytics schemas for API requests and responses
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class AnalyticsResponse(BaseModel):
    metric_name: str
    metric_value: float
    metric_type: str
    date: datetime
    profile_id: Optional[str] = None
    campaign_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True

class DashboardStats(BaseModel):
    total_profiles: int
    active_profiles: int
    total_campaigns: int
    active_campaigns: int
    total_messages_sent: int
    total_uids_scraped: int
    success_rate: float
    today_messages: int
    today_scraped: int

class PerformanceMetrics(BaseModel):
    messages_per_hour: float
    scraping_rate: float
    error_rate: float
    uptime_percentage: float

class TimeSeriesData(BaseModel):
    date: datetime
    value: float
    
class AnalyticsChart(BaseModel):
    title: str
    data: List[TimeSeriesData]
    metric_type: str
