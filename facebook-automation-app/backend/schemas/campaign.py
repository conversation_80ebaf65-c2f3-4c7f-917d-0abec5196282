"""
Campaign schemas for API requests and responses
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class CampaignBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    campaign_type: str = Field(..., description="Type of campaign: scraping, messaging, mixed")
    target_audience: Optional[Dict[str, Any]] = None
    message_template: Optional[str] = None

class CampaignCreate(CampaignBase):
    profile_id: str
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None

class CampaignUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    campaign_type: Optional[str] = None
    target_audience: Optional[Dict[str, Any]] = None
    message_template: Optional[str] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    status: Optional[str] = None

class CampaignResponse(CampaignBase):
    id: str
    profile_id: str
    status: str
    progress: float
    total_targets: int
    completed_targets: int
    successful_actions: int
    failed_actions: int
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class CampaignList(BaseModel):
    campaigns: List[CampaignResponse]
    total: int
    page: int
    per_page: int

class CampaignStats(BaseModel):
    total_campaigns: int
    active_campaigns: int
    completed_campaigns: int
    failed_campaigns: int
    draft_campaigns: int
