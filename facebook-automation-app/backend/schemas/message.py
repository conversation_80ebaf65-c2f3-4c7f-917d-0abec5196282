"""
Message schemas for API requests and responses
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class MessageBase(BaseModel):
    content: str = Field(..., min_length=1)
    message_type: str = Field("text", description="Type: text, image, video, link")
    target_uid: str = Field(..., description="Target user ID")
    target_name: Optional[str] = None

class MessageCreate(MessageBase):
    profile_id: str
    campaign_id: Optional[str] = None

class MessageResponse(MessageBase):
    id: str
    profile_id: str
    campaign_id: Optional[str] = None
    target_url: Optional[str] = None
    status: str
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MessageList(BaseModel):
    messages: List[MessageResponse]
    total: int
    page: int
    per_page: int

class MessageStats(BaseModel):
    total_messages: int
    sent_messages: int
    delivered_messages: int
    failed_messages: int
    pending_messages: int
