# Facebook Automation Desktop App

A comprehensive desktop application for Facebook automation with profile management, data scraping, and bulk messaging capabilities.

## 🚀 Features

- **Profile Management**: Create and manage browser profiles with antidetect features
- **Facebook Scraping**: Extract UIDs and user data from posts, comments, and interactions
- **Bulk Messaging**: Send personalized messages to targeted users
- **Analytics Dashboard**: Track campaign performance and success rates
- **Desktop Application**: Cross-platform Electron app for Windows, macOS, and Linux

## 🏗️ Architecture

### Frontend (React + Electron)
- **React 18** with modern hooks and functional components
- **Material-UI v5** for professional UI components
- **Electron** for desktop application wrapper
- **React Router** for navigation
- **React Query** for server state management
- **Axios** for API communication

### Backend (FastAPI + Python)
- **FastAPI** for high-performance REST API
- **SQLAlchemy** for database ORM
- **Zendriver** for browser automation
- **Asyncio** for concurrent operations
- **PostgreSQL/SQLite** for data storage

## 📋 Prerequisites

### System Requirements
- **Node.js** 16.x or higher
- **Python** 3.8 or higher
- **Git** for version control

### Development Tools
- **npm** or **yarn** for package management
- **pip** for Python packages
- **Code editor** (VS Code recommended)

## 🛠️ Installation & Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd facebook-automation-app
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Initialize database
python -m alembic upgrade head

# Start backend server
python main.py
```

### 3. Frontend Setup
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

## 🚀 Build & Deployment

### Development Mode
```bash
# Terminal 1: Start backend
cd backend
python main.py

# Terminal 2: Start frontend
cd frontend
npm start
```

### Production Build

#### Frontend Web Build
```bash
cd frontend
npm run build
```

#### Desktop App Build
```bash
cd frontend

# Build for current platform
npm run electron-build

# Build for specific platforms
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux

# Build for all platforms
npm run build:all
```

#### Backend Production
```bash
cd backend

# Install production dependencies
pip install -r requirements.txt

# Set production environment
export ENVIRONMENT=production

# Run with Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 📁 Project Structure

```
facebook-automation-app/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core functionality
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
│   ├── alembic/            # Database migrations
│   ├── requirements.txt    # Python dependencies
│   └── main.py            # Application entry point
├── frontend/               # React + Electron frontend
│   ├── public/
│   │   ├── electron.js     # Electron main process
│   │   └── index.html      # HTML template
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts
│   │   ├── hooks/          # Custom hooks
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── App.js          # Main app component
│   ├── package.json        # Node dependencies
│   └── electron-builder.json # Electron build config
└── README.md              # This file
```

## 🔧 Configuration

### Backend Configuration (.env)
```env
# Database
DATABASE_URL=sqlite:///./app.db

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Browser Settings
CHROME_EXECUTABLE_PATH=/path/to/chrome
FIREFOX_EXECUTABLE_PATH=/path/to/firefox
```

### Frontend Configuration
```javascript
// src/config/api.js
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
export const API_TIMEOUT = 30000;
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest tests/ -v
```

### Frontend Tests
```bash
cd frontend
npm test
```

### End-to-End Tests
```bash
cd frontend
npm run test:e2e
```

## 📦 Available Scripts

### Backend Scripts
```bash
python main.py              # Start development server
python -m pytest           # Run tests
python -m alembic upgrade head  # Run migrations
python -m alembic revision --autogenerate -m "message"  # Create migration
```

### Frontend Scripts
```bash
npm start                   # Start development server
npm run build              # Build for production
npm test                   # Run tests
npm run electron           # Start Electron app
npm run electron-build     # Build desktop app
npm run lint               # Run ESLint
npm run format             # Format code with Prettier
```

## 🐛 Troubleshooting

### Common Issues

#### Backend Issues
1. **Database connection error**
   ```bash
   # Check database URL in .env
   # Ensure database server is running
   python -c "from app.database import engine; print('DB OK')"
   ```

2. **Import errors**
   ```bash
   # Ensure virtual environment is activated
   # Reinstall dependencies
   pip install -r requirements.txt --force-reinstall
   ```

#### Frontend Issues
1. **Module not found errors**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Electron build fails**
   ```bash
   # Clear Electron cache
   npm run electron-rebuild
   ```

3. **API connection errors**
   ```bash
   # Check backend is running on correct port
   # Verify API_BASE_URL in frontend config
   ```

4. **useApp must be used within an AppProvider**
   ```bash
   # This error is fixed in the latest version
   # Ensure you have the latest App.js with proper provider wrapping
   ```

### Performance Issues
1. **Slow startup**
   - Check antivirus software
   - Ensure sufficient RAM (minimum 4GB)
   - Close unnecessary applications

2. **High memory usage**
   - Limit concurrent browser instances
   - Adjust scraping batch sizes
   - Monitor system resources

## 🔒 Security Considerations

### Browser Security
- Use separate profiles for each account
- Implement proper proxy rotation
- Respect rate limits to avoid detection

### Data Security
- Encrypt sensitive data at rest
- Use HTTPS for all API communications
- Implement proper authentication

### Compliance
- Respect Facebook's Terms of Service
- Implement proper consent mechanisms
- Follow data protection regulations (GDPR, etc.)

## 📈 Performance Optimization

### Backend Optimization
- Use connection pooling for database
- Implement caching for frequently accessed data
- Use async/await for I/O operations

### Frontend Optimization
- Implement code splitting
- Use React.memo for expensive components
- Optimize bundle size with tree shaking

### Browser Automation
- Reuse browser instances when possible
- Implement proper cleanup
- Use headless mode for better performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This tool is for educational and legitimate business purposes only. Users are responsible for complying with Facebook's Terms of Service and applicable laws. The developers are not responsible for any misuse of this software.

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review troubleshooting section

## 🗺️ Roadmap

- [ ] Advanced analytics and reporting
- [ ] Multi-account management
- [ ] Scheduled campaigns
- [ ] Advanced targeting options
- [ ] Integration with external APIs
- [ ] Mobile app companion

---

**Built with ❤️ using React, Electron, FastAPI, and Python**
