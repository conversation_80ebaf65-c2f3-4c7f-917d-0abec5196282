{"name": "facebook-automation-desktop", "version": "1.0.0", "description": "Facebook Automation Desktop App with Profile Management and Bulk Messaging", "main": "public/electron.js", "private": true, "homepage": "./", "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.8", "@mui/x-data-grid": "^5.17.22", "@mui/x-date-pickers": "^5.0.17", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "axios": "^1.3.3", "dayjs": "^1.11.7", "electron-is-dev": "^2.0.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.1", "react-hot-toast": "^2.4.0", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "react-scripts": "^5.0.1", "recharts": "^2.5.0", "web-vitals": "^3.1.1"}, "devDependencies": {"concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "^37.2.0", "electron-builder": "^26.0.12", "wait-on": "^7.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build"}, "build": {"appId": "com.facebook-automation.desktop", "productName": "Facebook Automation", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}