import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,

  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  LinearProgress,

} from '@mui/material';
import {
  Add as AddIcon,
  Person as PersonIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,

  Refresh as RefreshIcon,

  Computer as ComputerIcon,
  Security as SecurityIcon,
  Language as LanguageIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { profilesAPI } from '../services/api';
import { useApp } from '../contexts/AppContext';

function ProfileCard({ profile, onEdit, onDelete, onTest }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              {profile.name.charAt(0).toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {profile.name}
              </Typography>
              <Chip
                label={profile.status || 'inactive'}
                size="small"
                color={getStatusColor(profile.status)}
              />
            </Box>
          </Box>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Browser
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.browser_type || 'Chrome'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Location
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.timezone || 'UTC'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Proxy
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.proxy_enabled ? 'Enabled' : 'Disabled'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Language
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.language || 'en-US'}
            </Typography>
          </Grid>
        </Grid>

        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <Button
            size="small"
            variant="outlined"
            startIcon={<PlayIcon />}
            onClick={() => onTest(profile)}
            sx={{ textTransform: 'none' }}
          >
            Test
          </Button>
          <Button
            size="small"
            variant="text"
            startIcon={<EditIcon />}
            onClick={() => onEdit(profile)}
            sx={{ textTransform: 'none' }}
          >
            Edit
          </Button>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Profile</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <PlayIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Test Browser</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete Profile</ListItemText>
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
}

function Profiles() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    browser_type: 'chrome',
    user_agent: '',
    screen_resolution: '1920x1080',
    timezone: 'UTC',
    language: 'en-US',
    proxy_enabled: false,
    proxy_host: '',
    proxy_port: '',
    proxy_username: '',
    proxy_password: '',
  });

  const queryClient = useQueryClient();
  const { setProfiles } = useApp();

  // Fetch profiles
  const { data: profilesData, isLoading, error } = useQuery(
    'profiles',
    profilesAPI.getAll,
    {
      onSuccess: (data) => {
        setProfiles(data.data || []);
      },
      onError: (error) => {
        toast.error('Failed to load profiles');
      },
    }
  );

  // Create profile mutation
  const createMutation = useMutation(profilesAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries('profiles');
      setCreateDialogOpen(false);
      resetForm();
      toast.success('Profile created successfully');
    },
    onError: (error) => {
      toast.error('Failed to create profile');
    },
  });

  // Update profile mutation
  const updateMutation = useMutation(
    ({ id, data }) => profilesAPI.update(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('profiles');
        setEditDialogOpen(false);
        resetForm();
        toast.success('Profile updated successfully');
      },
      onError: (error) => {
        toast.error('Failed to update profile');
      },
    }
  );

  // Delete profile mutation
  const deleteMutation = useMutation(profilesAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries('profiles');
      toast.success('Profile deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete profile');
    },
  });

  // Test browser mutation
  const testMutation = useMutation(
    (profileId) => profilesAPI.testBrowser(profileId),
    {
      onSuccess: () => {
        toast.success('Browser test completed successfully');
      },
      onError: (error) => {
        toast.error('Browser test failed');
      },
    }
  );

  const profiles = Array.isArray(profilesData?.profiles)
    ? profilesData.profiles
    : Array.isArray(profilesData?.data)
    ? profilesData.data
    : Array.isArray(profilesData)
    ? profilesData
    : [];

  const resetForm = () => {
    setFormData({
      name: '',
      browser_type: 'chrome',
      user_agent: '',
      screen_resolution: '1920x1080',
      timezone: 'UTC',
      language: 'en-US',
      proxy_enabled: false,
      proxy_host: '',
      proxy_port: '',
      proxy_username: '',
      proxy_password: '',
    });
    setSelectedProfile(null);
  };

  const handleCreate = () => {
    setCreateDialogOpen(true);
    resetForm();
  };

  const handleEdit = (profile) => {
    setSelectedProfile(profile);
    setFormData({
      name: profile.name || '',
      browser_type: profile.browser_type || 'chrome',
      user_agent: profile.user_agent || '',
      screen_resolution: profile.screen_resolution || '1920x1080',
      timezone: profile.timezone || 'UTC',
      language: profile.language || 'en-US',
      proxy_enabled: profile.proxy_enabled || false,
      proxy_host: profile.proxy_host || '',
      proxy_port: profile.proxy_port || '',
      proxy_username: profile.proxy_username || '',
      proxy_password: profile.proxy_password || '',
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (profile) => {
    if (window.confirm(`Are you sure you want to delete profile "${profile.name}"?`)) {
      deleteMutation.mutate(profile.id);
    }
  };

  const handleTest = (profile) => {
    testMutation.mutate(profile.id);
  };

  const handleSubmit = () => {
    if (selectedProfile) {
      updateMutation.mutate({ id: selectedProfile.id, data: formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (error) {
    return (
      <Box className="fade-in">
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load profiles. Please check your connection to the backend.
        </Alert>
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700 }}>
          Profile Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => queryClient.invalidateQueries('profiles')}
            sx={{ textTransform: 'none' }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreate}
            sx={{ textTransform: 'none' }}
          >
            New Profile
          </Button>
        </Box>
      </Box>

      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      {profiles.length === 0 && !isLoading ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              No Profiles Found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Create your first browser profile to get started
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreate}
              sx={{ textTransform: 'none' }}
            >
              Create Profile
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {profiles.map((profile) => (
            <Grid item xs={12} sm={6} md={4} key={profile.id}>
              <ProfileCard
                profile={profile}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onTest={handleTest}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Create/Edit Profile Dialog */}
      <Dialog
        open={createDialogOpen || editDialogOpen}
        onClose={() => {
          setCreateDialogOpen(false);
          setEditDialogOpen(false);
          resetForm();
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Profile Name"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Browser Type</InputLabel>
                  <Select
                    value={formData.browser_type}
                    onChange={(e) => handleFormChange('browser_type', e.target.value)}
                    label="Browser Type"
                  >
                    <MenuItem value="chrome">Chrome</MenuItem>
                    <MenuItem value="firefox">Firefox</MenuItem>
                    <MenuItem value="edge">Edge</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Screen Resolution</InputLabel>
                  <Select
                    value={formData.screen_resolution}
                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}
                    label="Screen Resolution"
                  >
                    <MenuItem value="1920x1080">1920x1080</MenuItem>
                    <MenuItem value="1366x768">1366x768</MenuItem>
                    <MenuItem value="1440x900">1440x900</MenuItem>
                    <MenuItem value="1280x720">1280x720</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Timezone</InputLabel>
                  <Select
                    value={formData.timezone}
                    onChange={(e) => handleFormChange('timezone', e.target.value)}
                    label="Timezone"
                  >
                    <MenuItem value="UTC">UTC</MenuItem>
                    <MenuItem value="Asia/Ho_Chi_Minh">Asia/Ho_Chi_Minh</MenuItem>
                    <MenuItem value="America/New_York">America/New_York</MenuItem>
                    <MenuItem value="Europe/London">Europe/London</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    label="Language"
                  >
                    <MenuItem value="en-US">English (US)</MenuItem>
                    <MenuItem value="vi-VN">Vietnamese</MenuItem>
                    <MenuItem value="zh-CN">Chinese (Simplified)</MenuItem>
                    <MenuItem value="ja-JP">Japanese</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="User Agent (Optional)"
                  value={formData.user_agent}
                  onChange={(e) => handleFormChange('user_agent', e.target.value)}
                  placeholder="Leave empty for automatic generation"
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.proxy_enabled}
                      onChange={(e) => handleFormChange('proxy_enabled', e.target.checked)}
                    />
                  }
                  label="Enable Proxy"
                />
              </Grid>

              {formData.proxy_enabled && (
                <>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Proxy Host"
                      value={formData.proxy_host}
                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Proxy Port"
                      value={formData.proxy_port}
                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}
                      type="number"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Proxy Username (Optional)"
                      value={formData.proxy_username}
                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Proxy Password (Optional)"
                      value={formData.proxy_password}
                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}
                      type="password"
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setCreateDialogOpen(false);
              setEditDialogOpen(false);
              resetForm();
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}
          >
            {selectedProfile ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Profiles;
