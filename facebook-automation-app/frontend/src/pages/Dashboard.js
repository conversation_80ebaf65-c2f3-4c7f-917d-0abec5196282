import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  LinearProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  IconButton,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Person as PersonIcon,
  Search as SearchIcon,
  Message as MessageIcon,
  Analytics as AnalyticsIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,

} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';

import { useApp } from '../contexts/AppContext';
import { analyticsAPI, campaignsAPI, profilesAPI, scrapingAPI } from '../services/api';

function StatCard({ title, value, subtitle, icon: Icon, color = 'primary', trend, onClick }) {
  const theme = useTheme();

  return (
    <Card
      className="hover-card"
      onClick={onClick}
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        bgcolor: alpha(theme.palette[color].main, 0.05),
        border: 1,
        borderColor: alpha(theme.palette[color].main, 0.2),
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: `${color}.main`, mb: 0.5 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: `${color}.main`,
              width: 56,
              height: 56,
            }}
          >
            <Icon sx={{ fontSize: 28 }} />
          </Avatar>
        </Box>
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {trend > 0 ? (
              <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
            ) : (
              <TrendingDownIcon sx={{ fontSize: 16, color: 'error.main', mr: 0.5 }} />
            )}
            <Typography
              variant="caption"
              sx={{
                color: trend > 0 ? 'success.main' : 'error.main',
                fontWeight: 600,
              }}
            >
              {Math.abs(trend)}% from last week
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

function RecentActivity({ activities }) {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Recent Activity
        </Typography>
        <List sx={{ pt: 0 }}>
          {activities.map((activity, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemAvatar>
                <Avatar
                  sx={{
                    bgcolor: activity.color || 'primary.main',
                    width: 32,
                    height: 32,
                  }}
                >
                  {activity.icon}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={activity.title}
                secondary={activity.time}
                primaryTypographyProps={{ fontSize: 14, fontWeight: 500 }}
                secondaryTypographyProps={{ fontSize: 12 }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
}

function ActiveCampaigns({ campaigns }) {
  const navigate = useNavigate();

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'success';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Active Campaigns
          </Typography>
          <Button
            size="small"
            onClick={() => navigate('/messaging')}
            sx={{ textTransform: 'none' }}
          >
            View All
          </Button>
        </Box>
        
        {campaigns.length === 0 ? (
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
            No active campaigns
          </Typography>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {campaigns.slice(0, 3).map((campaign) => (
              <Box
                key={campaign.id}
                sx={{
                  p: 2,
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2,
                  bgcolor: 'background.default',
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {campaign.name}
                  </Typography>
                  <Chip
                    label={campaign.status}
                    size="small"
                    color={getStatusColor(campaign.status)}
                  />
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    Progress: {campaign.sent_messages || 0}/{campaign.total_messages || 0}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {campaign.progress_percentage || 0}%
                  </Typography>
                </Box>
                
                <LinearProgress
                  variant="determinate"
                  value={campaign.progress_percentage || 0}
                  sx={{ mb: 1, height: 6, borderRadius: 3 }}
                />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    Success Rate: {campaign.success_rate || 0}%
                  </Typography>
                  <Box>
                    {campaign.status === 'running' && (
                      <IconButton size="small" color="warning">
                        <PauseIcon fontSize="small" />
                      </IconButton>
                    )}
                    {campaign.status === 'paused' && (
                      <IconButton size="small" color="success">
                        <PlayIcon fontSize="small" />
                      </IconButton>
                    )}
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

function Dashboard() {
  const navigate = useNavigate();
  const { setConnectionStatus, setLastSync } = useApp();

  // Fetch dashboard data
  const { data: dashboardData, isLoading } = useQuery(
    'dashboard-stats',
    () => analyticsAPI.getDashboardStats(7),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
      onSuccess: () => {
        setConnectionStatus('connected');
        setLastSync(new Date().toISOString());
      },
      onError: () => {
        setConnectionStatus('disconnected');
      },
    }
  );

  const { data: profilesData } = useQuery('profiles', () => profilesAPI.getAll());
  const { data: campaignsData } = useQuery('campaigns', () => campaignsAPI.getAll());
  const { data: scrapingData } = useQuery('scraping-sessions', () => scrapingAPI.getSessions());

  const stats = dashboardData?.data || {};
  const profiles = profilesData?.data?.profiles || [];
  const campaigns = campaignsData?.data?.campaigns || [];
  const scrapingSessions = scrapingData?.data?.sessions || [];

  // Mock recent activities
  const recentActivities = [
    {
      title: 'New profile created',
      time: '2 minutes ago',
      icon: <PersonIcon fontSize="small" />,
      color: 'primary.main',
    },
    {
      title: 'Scraping session completed',
      time: '15 minutes ago',
      icon: <SearchIcon fontSize="small" />,
      color: 'success.main',
    },
    {
      title: 'Campaign started',
      time: '1 hour ago',
      icon: <MessageIcon fontSize="small" />,
      color: 'info.main',
    },
    {
      title: 'Analytics report generated',
      time: '2 hours ago',
      icon: <AnalyticsIcon fontSize="small" />,
      color: 'secondary.main',
    },
  ];

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <LinearProgress sx={{ width: 200 }} />
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        Dashboard
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Profiles"
            value={profiles.length}
            subtitle="Active browser profiles"
            icon={PersonIcon}
            color="primary"
            trend={12}
            onClick={() => navigate('/profiles')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Scraping Sessions"
            value={scrapingSessions.length}
            subtitle="Data collection sessions"
            icon={SearchIcon}
            color="success"
            trend={8}
            onClick={() => navigate('/scraping')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Campaigns"
            value={campaigns.filter(c => c.status === 'running').length}
            subtitle="Running message campaigns"
            icon={MessageIcon}
            color="info"
            trend={-5}
            onClick={() => navigate('/messaging')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Success Rate"
            value={`${stats.avg_success_rate || 0}%`}
            subtitle="Average campaign success"
            icon={AnalyticsIcon}
            color="warning"
            trend={15}
            onClick={() => navigate('/analytics')}
          />
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <ActiveCampaigns campaigns={campaigns} />
        </Grid>
        <Grid item xs={12} md={4}>
          <RecentActivity activities={recentActivities} />
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
