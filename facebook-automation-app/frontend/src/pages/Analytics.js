import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,

  LinearProgress,


  Avatar,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Message as MessageIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

import { analyticsAPI, campaignsAPI } from '../services/api';

function StatCard({ title, value, subtitle, icon: Icon, color = 'primary', trend }) {
  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: `${color}.main`, mb: 0.5 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: `${color}.main`,
              width: 56,
              height: 56,
            }}
          >
            <Icon sx={{ fontSize: 28 }} />
          </Avatar>
        </Box>
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {trend > 0 ? (
              <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
            ) : (
              <TrendingDownIcon sx={{ fontSize: 16, color: 'error.main', mr: 0.5 }} />
            )}
            <Typography
              variant="caption"
              sx={{
                color: trend > 0 ? 'success.main' : 'error.main',
                fontWeight: 600,
              }}
            >
              {Math.abs(trend)}% from last period
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

function CampaignPerformanceChart({ data }) {
  const chartData = data?.time_series || [];

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Campaign Performance Over Time
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="messages_sent"
              stroke="#1976d2"
              strokeWidth={2}
              name="Messages Sent"
            />
            <Line
              type="monotone"
              dataKey="messages_delivered"
              stroke="#2e7d32"
              strokeWidth={2}
              name="Messages Delivered"
            />
            <Line
              type="monotone"
              dataKey="messages_failed"
              stroke="#d32f2f"
              strokeWidth={2}
              name="Messages Failed"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function SuccessRateChart({ data }) {
  const chartData = data?.hourly_distribution || {};
  const hourlyData = Object.entries(chartData).map(([hour, stats]) => ({
    hour: `${hour}:00`,
    success_rate: stats.delivered > 0 ? (stats.delivered / (stats.sent + stats.delivered + stats.failed)) * 100 : 0,
    total_messages: stats.sent + stats.delivered + stats.failed,
  }));

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Success Rate by Hour
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={hourlyData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="hour" />
            <YAxis />
            <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Success Rate']} />
            <Bar dataKey="success_rate" fill="#2e7d32" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function ErrorAnalysisChart({ data }) {
  const errorTypes = data?.error_analysis?.error_types || {};
  const pieData = Object.entries(errorTypes).map(([error, count]) => ({
    name: error.length > 20 ? error.substring(0, 20) + '...' : error,
    value: count,
  }));

  const COLORS = ['#f44336', '#ff9800', '#ffeb3b', '#4caf50', '#2196f3', '#9c27b0'];

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Error Distribution
        </Typography>
        {pieData.length > 0 ? (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        ) : (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircleIcon sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
            <Typography variant="body1" color="text.secondary">
              No errors recorded
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

function RecentCampaigns({ campaigns }) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'success';
      case 'completed':
        return 'info';
      case 'paused':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon fontSize="small" />;
      case 'cancelled':
        return <ErrorIcon fontSize="small" />;
      default:
        return <ScheduleIcon fontSize="small" />;
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Recent Campaigns
        </Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Campaign</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Messages</TableCell>
                <TableCell align="right">Success Rate</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {campaigns.slice(0, 5).map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {campaign.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getStatusIcon(campaign.status)}
                      label={campaign.status}
                      size="small"
                      color={getStatusColor(campaign.status)}
                    />
                  </TableCell>
                  <TableCell align="right">
                    {campaign.sent_messages || 0} / {campaign.total_messages || 0}
                  </TableCell>
                  <TableCell align="right">
                    <Typography
                      variant="body2"
                      sx={{
                        color: (campaign.delivered_messages / campaign.total_messages) > 0.8 
                          ? 'success.main' 
                          : 'text.secondary'
                      }}
                    >
                      {campaign.total_messages > 0 
                        ? Math.round((campaign.delivered_messages / campaign.total_messages) * 100)
                        : 0}%
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
}

function Analytics() {
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [timeRange, setTimeRange] = useState(7);

  // Fetch campaigns for selection
  const { data: campaignsData } = useQuery('campaigns', campaignsAPI.getAll);
  const campaigns = Array.isArray(campaignsData?.campaigns)
    ? campaignsData.campaigns
    : Array.isArray(campaignsData?.data)
    ? campaignsData.data
    : Array.isArray(campaignsData)
    ? campaignsData
    : [];

  // Fetch dashboard analytics
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery(
    ['dashboard-analytics', timeRange],
    () => analyticsAPI.getDashboardSummary({ days: timeRange }),
    {
      refetchInterval: 30000,
    }
  );

  // Fetch campaign-specific analytics
  const { data: campaignAnalytics, isLoading: campaignLoading } = useQuery(
    ['campaign-analytics', selectedCampaign, timeRange],
    () => selectedCampaign 
      ? analyticsAPI.getCampaignAnalytics(selectedCampaign, { days: timeRange })
      : null,
    {
      enabled: !!selectedCampaign,
      refetchInterval: 30000,
    }
  );

  const dashboardStats = dashboardData?.data || {};
  const campaignData = campaignAnalytics?.data || {};

  const isLoading = dashboardLoading || campaignLoading;

  return (
    <Box className="fade-in">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700 }}>
          Analytics & Reports
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              label="Time Range"
            >
              <MenuItem value={1}>Last 24 hours</MenuItem>
              <MenuItem value={7}>Last 7 days</MenuItem>
              <MenuItem value={30}>Last 30 days</MenuItem>
              <MenuItem value={90}>Last 90 days</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Campaign</InputLabel>
            <Select
              value={selectedCampaign}
              onChange={(e) => setSelectedCampaign(e.target.value)}
              label="Campaign"
            >
              <MenuItem value="">All Campaigns</MenuItem>
              {campaigns.map((campaign) => (
                <MenuItem key={campaign.id} value={campaign.id}>
                  {campaign.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            sx={{ textTransform: 'none' }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            sx={{ textTransform: 'none' }}
          >
            Export
          </Button>
        </Box>
      </Box>

      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Overview Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Campaigns"
            value={dashboardStats.total_campaigns || 0}
            subtitle={`${timeRange} days period`}
            icon={MessageIcon}
            color="primary"
            trend={12}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Messages Sent"
            value={dashboardStats.total_sent || 0}
            subtitle="Total messages delivered"
            icon={CheckCircleIcon}
            color="success"
            trend={8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Success Rate"
            value={`${dashboardStats.avg_success_rate || 0}%`}
            subtitle="Average delivery rate"
            icon={TrendingUpIcon}
            color="info"
            trend={15}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Failed Messages"
            value={dashboardStats.total_failed || 0}
            subtitle="Messages that failed"
            icon={ErrorIcon}
            color="error"
            trend={-5}
          />
        </Grid>
      </Grid>

      {/* Charts Section */}
      {selectedCampaign ? (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <CampaignPerformanceChart data={campaignData} />
          </Grid>
          <Grid item xs={12} md={6}>
            <SuccessRateChart data={campaignData} />
          </Grid>
          <Grid item xs={12} md={6}>
            <ErrorAnalysisChart data={campaignData} />
          </Grid>
        </Grid>
      ) : (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 8 }}>
                <AnalyticsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" sx={{ mb: 1 }}>
                  Select a Campaign for Detailed Analytics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Choose a specific campaign from the dropdown above to view detailed performance metrics
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Recent Campaigns Table */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <RecentCampaigns campaigns={campaigns} />
        </Grid>
      </Grid>
    </Box>
  );
}

export default Analytics;
