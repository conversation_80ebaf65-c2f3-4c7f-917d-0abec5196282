import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Save as SaveIcon,
  Restore as RestoreIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Palette as PaletteIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { systemAPI } from '../services/api';
import { useApp } from '../contexts/AppContext';
import TabPanel from '../components/Common/TabPanel';

function GeneralSettings({ settings, onSettingsChange }) {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              API Configuration
            </Typography>
            <TextField
              fullWidth
              label="Backend API URL"
              value={settings.apiBaseUrl}
              onChange={(e) => onSettingsChange('apiBaseUrl', e.target.value)}
              sx={{ mb: 2 }}
              helperText="URL of the backend API server"
            />
            <TextField
              fullWidth
              label="Request Timeout (seconds)"
              type="number"
              value={settings.requestTimeout || 30}
              onChange={(e) => onSettingsChange('requestTimeout', parseInt(e.target.value))}
              inputProps={{ min: 5, max: 300 }}
              helperText="Maximum time to wait for API responses"
            />
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Auto Refresh
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.autoRefresh}
                  onChange={(e) => onSettingsChange('autoRefresh', e.target.checked)}
                />
              }
              label="Enable Auto Refresh"
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Refresh Interval (seconds)"
              type="number"
              value={settings.refreshInterval / 1000}
              onChange={(e) => onSettingsChange('refreshInterval', parseInt(e.target.value) * 1000)}
              disabled={!settings.autoRefresh}
              inputProps={{ min: 5, max: 300 }}
              helperText="How often to refresh data automatically"
            />
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Application Preferences
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={settings.theme}
                    onChange={(e) => onSettingsChange('theme', e.target.value)}
                    label="Theme"
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="auto">Auto (System)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={settings.language || 'en'}
                    onChange={(e) => onSettingsChange('language', e.target.value)}
                    label="Language"
                  >
                    <MenuItem value="en">English</MenuItem>
                    <MenuItem value="vi">Tiếng Việt</MenuItem>
                    <MenuItem value="zh">中文</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

function MessagingSettings({ settings, onSettingsChange }) {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Rate Limiting
            </Typography>
            <TextField
              fullWidth
              label="Max Concurrent Senders"
              type="number"
              value={settings.maxConcurrentSenders || 2}
              onChange={(e) => onSettingsChange('maxConcurrentSenders', parseInt(e.target.value))}
              sx={{ mb: 2 }}
              inputProps={{ min: 1, max: 10 }}
              helperText="Maximum number of simultaneous message senders"
            />
            <TextField
              fullWidth
              label="Default Messages per Minute"
              type="number"
              value={settings.defaultMessagesPerMinute || 30}
              onChange={(e) => onSettingsChange('defaultMessagesPerMinute', parseInt(e.target.value))}
              sx={{ mb: 2 }}
              inputProps={{ min: 1, max: 300 }}
              helperText="Default rate for new campaigns"
            />
            <TextField
              fullWidth
              label="Message Delay Min (seconds)"
              type="number"
              value={settings.messageDelayMin || 5}
              onChange={(e) => onSettingsChange('messageDelayMin', parseFloat(e.target.value))}
              inputProps={{ min: 0.5, max: 60, step: 0.5 }}
              helperText="Minimum delay between messages"
            />
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Message Configuration
            </Typography>
            <TextField
              fullWidth
              label="Max Message Length"
              type="number"
              value={settings.maxMessageLength || 2000}
              onChange={(e) => onSettingsChange('maxMessageLength', parseInt(e.target.value))}
              sx={{ mb: 2 }}
              inputProps={{ min: 100, max: 5000 }}
              helperText="Maximum characters per message"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={settings.enableMessagePreview || true}
                  onChange={(e) => onSettingsChange('enableMessagePreview', e.target.checked)}
                />
              }
              label="Enable Message Preview"
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={settings.enableRetryFailedMessages || true}
                  onChange={(e) => onSettingsChange('enableRetryFailedMessages', e.target.checked)}
                />
              }
              label="Auto Retry Failed Messages"
            />
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Safety Settings
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Daily Message Limit"
                  type="number"
                  value={settings.dailyMessageLimit || 1000}
                  onChange={(e) => onSettingsChange('dailyMessageLimit', parseInt(e.target.value))}
                  inputProps={{ min: 10, max: 10000 }}
                  helperText="Maximum messages per day per profile"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Cooldown Period (hours)"
                  type="number"
                  value={settings.cooldownPeriod || 24}
                  onChange={(e) => onSettingsChange('cooldownPeriod', parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 168 }}
                  helperText="Rest period after reaching daily limit"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

function NotificationSettings({ settings, onSettingsChange }) {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Notification Preferences
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="Campaign Completion"
                  secondary="Notify when messaging campaigns complete"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifyCampaignComplete || true}
                    onChange={(e) => onSettingsChange('notifyCampaignComplete', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Scraping Completion"
                  secondary="Notify when scraping sessions complete"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifyScrapingComplete || true}
                    onChange={(e) => onSettingsChange('notifyScrapingComplete', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Error Alerts"
                  secondary="Notify when errors occur"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifyErrors || true}
                    onChange={(e) => onSettingsChange('notifyErrors', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Daily Summary"
                  secondary="Send daily activity summary"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifyDailySummary || false}
                    onChange={(e) => onSettingsChange('notifyDailySummary', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

function DataManagement({ onClearData }) {
  const [clearDialogOpen, setClearDialogOpen] = useState(false);
  const [clearType, setClearType] = useState('');

  const handleClearData = (type) => {
    setClearType(type);
    setClearDialogOpen(true);
  };

  const confirmClearData = () => {
    onClearData(clearType);
    setClearDialogOpen(false);
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Data Management
            </Typography>
            <Alert severity="warning" sx={{ mb: 3 }}>
              These actions cannot be undone. Please backup your data before proceeding.
            </Alert>
            
            <List>
              <ListItem>
                <ListItemText
                  primary="Clear Campaign Data"
                  secondary="Remove all messaging campaigns and related data"
                />
                <ListItemSecondaryAction>
                  <Button
                    variant="outlined"
                    color="warning"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleClearData('campaigns')}
                    sx={{ textTransform: 'none' }}
                  >
                    Clear
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Clear Scraping Data"
                  secondary="Remove all scraping sessions and scraped user data"
                />
                <ListItemSecondaryAction>
                  <Button
                    variant="outlined"
                    color="warning"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleClearData('scraping')}
                    sx={{ textTransform: 'none' }}
                  >
                    Clear
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Clear Analytics Data"
                  secondary="Remove all analytics and reporting data"
                />
                <ListItemSecondaryAction>
                  <Button
                    variant="outlined"
                    color="warning"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleClearData('analytics')}
                    sx={{ textTransform: 'none' }}
                  >
                    Clear
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Reset All Settings"
                  secondary="Reset all application settings to defaults"
                />
                <ListItemSecondaryAction>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<RestoreIcon />}
                    onClick={() => handleClearData('settings')}
                    sx={{ textTransform: 'none' }}
                  >
                    Reset
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </CardContent>
        </Card>
      </Grid>

      <Dialog open={clearDialogOpen} onClose={() => setClearDialogOpen(false)}>
        <DialogTitle>Confirm Data Clearing</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to clear {clearType} data? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setClearDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmClearData} color="error" variant="contained">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
}

function Settings() {
  const [tabValue, setTabValue] = useState(0);
  const { settings, updateSettings, appVersion, isElectron } = useApp();
  const [localSettings, setLocalSettings] = useState(settings);

  const queryClient = useQueryClient();

  // Fetch system settings
  const { data: systemSettings } = useQuery(
    'system-settings',
    systemAPI.getSettings,
    {
      onSuccess: (data) => {
        setLocalSettings(prev => ({ ...prev, ...data.data }));
      },
    }
  );

  // Save settings mutation
  const saveMutation = useMutation(systemAPI.updateSettings, {
    onSuccess: () => {
      updateSettings(localSettings);
      toast.success('Settings saved successfully');
    },
    onError: (error) => {
      toast.error('Failed to save settings');
    },
  });

  const handleSettingsChange = (key, value) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = () => {
    saveMutation.mutate(localSettings);
  };

  const handleReset = () => {
    setLocalSettings(settings);
    toast.info('Settings reset to last saved values');
  };

  const handleClearData = (type) => {
    // Implement data clearing logic
    toast.success(`${type} data cleared successfully`);
  };

  return (
    <Box className="fade-in">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700 }}>
          Settings
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RestoreIcon />}
            onClick={handleReset}
            sx={{ textTransform: 'none' }}
          >
            Reset
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={saveMutation.isLoading}
            sx={{ textTransform: 'none' }}
          >
            Save Settings
          </Button>
        </Box>
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab icon={<SettingsIcon />} label="General" />
          <Tab icon={<SpeedIcon />} label="Messaging" />
          <Tab icon={<NotificationsIcon />} label="Notifications" />
          <Tab icon={<StorageIcon />} label="Data" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <GeneralSettings
          settings={localSettings}
          onSettingsChange={handleSettingsChange}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <MessagingSettings
          settings={localSettings}
          onSettingsChange={handleSettingsChange}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <NotificationSettings
          settings={localSettings}
          onSettingsChange={handleSettingsChange}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <DataManagement onClearData={handleClearData} />
      </TabPanel>

      {/* App Info */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Application Information
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Version
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {appVersion}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Platform
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {isElectron ? 'Desktop (Electron)' : 'Web Browser'}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Backend API
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {localSettings.apiBaseUrl}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Build Date
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {new Date().toLocaleDateString()}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}

export default Settings;
