import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,

  IconButton,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  Tabs,
  Tab,
  Avatar,
  Divider,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Add as AddIcon,
  Message as MessageIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,

  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Analytics as AnalyticsIcon,

  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,

} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { campaignsAPI, profilesAPI, scrapingAPI } from '../services/api';
import { useApp } from '../contexts/AppContext';
import TabPanel from '../components/Common/TabPanel';

function CampaignCard({ campaign, onStart, onPause, onStop, onEdit, onDelete, onViewAnalytics }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'success';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <CircularProgress size={16} />;
      case 'completed':
        return <CheckCircleIcon fontSize="small" />;
      case 'cancelled':
        return <ErrorIcon fontSize="small" />;
      case 'paused':
        return <PauseIcon fontSize="small" />;
      default:
        return <ScheduleIcon fontSize="small" />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getSuccessRate = () => {
    if (!campaign.total_messages || campaign.total_messages === 0) return 0;
    return Math.round((campaign.delivered_messages / campaign.total_messages) * 100);
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              <MessageIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {campaign.name}
              </Typography>
              <Chip
                icon={getStatusIcon(campaign.status)}
                label={campaign.status || 'draft'}
                size="small"
                color={getStatusColor(campaign.status)}
              />
            </Box>
          </Box>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Progress
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <LinearProgress
                variant="determinate"
                value={campaign.progress_percentage || 0}
                sx={{ flex: 1, mr: 1 }}
              />
              <Typography variant="body2">
                {campaign.progress_percentage || 0}%
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Success Rate
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: 'success.main' }}>
              {getSuccessRate()}%
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Messages
            </Typography>
            <Typography variant="body2">
              {campaign.sent_messages || 0} / {campaign.total_messages || 0}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Rate
            </Typography>
            <Typography variant="body2">
              {campaign.messages_per_minute || 0}/min
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body2" color="text.secondary">
              Created
            </Typography>
            <Typography variant="body2">
              {formatDate(campaign.created_at)}
            </Typography>
          </Grid>
        </Grid>

        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          {campaign.status === 'running' ? (
            <Button
              size="small"
              variant="outlined"
              color="warning"
              startIcon={<PauseIcon />}
              onClick={() => onPause(campaign)}
              sx={{ textTransform: 'none' }}
            >
              Pause
            </Button>
          ) : campaign.status === 'paused' ? (
            <Button
              size="small"
              variant="outlined"
              color="success"
              startIcon={<PlayIcon />}
              onClick={() => onStart(campaign)}
              sx={{ textTransform: 'none' }}
            >
              Resume
            </Button>
          ) : (
            <Button
              size="small"
              variant="outlined"
              startIcon={<PlayIcon />}
              onClick={() => onStart(campaign)}
              sx={{ textTransform: 'none' }}
            >
              Start
            </Button>
          )}
          <Button
            size="small"
            variant="text"
            startIcon={<AnalyticsIcon />}
            onClick={() => onViewAnalytics(campaign)}
            sx={{ textTransform: 'none' }}
          >
            Analytics
          </Button>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { onEdit(campaign); handleMenuClose(); }}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Campaign</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onViewAnalytics(campaign); handleMenuClose(); }}>
            <ListItemIcon>
              <AnalyticsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>View Analytics</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onDelete(campaign); handleMenuClose(); }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete Campaign</ListItemText>
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
}

function CreateCampaignDialog({ open, onClose, onSubmit, profiles = [] }) {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    template_id: '',
    profile_id: '',
    scraping_session_id: '',
    messages_per_minute: 30,
    target_filters: {
      max_users: 1000,
      gender: '',
      interaction_types: [],
    },
  });

  // Templates functionality removed - using simple message templates
  const { data: profilesData } = useQuery('profiles', profilesAPI.getAll);
  const { data: sessionsData } = useQuery('scraping-sessions', scrapingAPI.getSessions);

  const templates = []; // Placeholder for templates
  const profilesFromAPI = Array.isArray(profilesData?.profiles)
    ? profilesData.profiles
    : Array.isArray(profilesData?.data)
    ? profilesData.data
    : Array.isArray(profilesData)
    ? profilesData
    : [];
  const sessions = Array.isArray(sessionsData?.sessions)
    ? sessionsData.sessions
    : Array.isArray(sessionsData?.data)
    ? sessionsData.data
    : Array.isArray(sessionsData)
    ? sessionsData
    : [];

  // Use profiles from props if available, otherwise use fetched profiles
  const finalProfiles = Array.isArray(profiles) && profiles.length > 0 ? profiles : profilesFromAPI;

  const steps = [
    'Basic Information',
    'Target Selection',
    'Message Settings',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = () => {
    onSubmit(formData);
    handleReset();
  };

  const handleReset = () => {
    setActiveStep(0);
    setFormData({
      name: '',
      template_id: '',
      profile_id: '',
      scraping_session_id: '',
      messages_per_minute: 30,
      target_filters: {
        max_users: 1000,
        gender: '',
        interaction_types: [],
      },
    });
  };

  const handleFormChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const isStepValid = (step) => {
    switch (step) {
      case 0:
        return formData.name && formData.template_id && formData.profile_id;
      case 1:
        return formData.scraping_session_id;
      case 2:
        return formData.messages_per_minute > 0;
      default:
        return false;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Messaging Campaign</DialogTitle>
      <DialogContent>
        <Stepper activeStep={activeStep} orientation="vertical">
          <Step>
            <StepLabel>Basic Information</StepLabel>
            <StepContent>
              <Box sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Campaign Name"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  sx={{ mb: 2 }}
                  required
                />
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Message Template</InputLabel>
                  <Select
                    value={formData.template_id}
                    onChange={(e) => handleFormChange('template_id', e.target.value)}
                    label="Message Template"
                  >
                    {templates.map((template) => (
                      <MenuItem key={template.id} value={template.id}>
                        {template.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>Sender Profile</InputLabel>
                  <Select
                    value={formData.profile_id}
                    onChange={(e) => handleFormChange('profile_id', e.target.value)}
                    label="Sender Profile"
                  >
                    {Array.isArray(finalProfiles) && finalProfiles.map((profile) => (
                      <MenuItem key={profile.id} value={profile.id}>
                        {profile.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </StepContent>
          </Step>

          <Step>
            <StepLabel>Target Selection</StepLabel>
            <StepContent>
              <Box sx={{ mt: 2 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Scraping Session</InputLabel>
                  <Select
                    value={formData.scraping_session_id}
                    onChange={(e) => handleFormChange('scraping_session_id', e.target.value)}
                    label="Scraping Session"
                  >
                    {sessions.map((session) => (
                      <MenuItem key={session.id} value={session.id}>
                        {session.name} ({session.users_found || 0} users)
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Max Users to Message"
                      type="number"
                      value={formData.target_filters.max_users}
                      onChange={(e) => handleFormChange('target_filters.max_users', parseInt(e.target.value))}
                      inputProps={{ min: 1, max: 10000 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Gender Filter</InputLabel>
                      <Select
                        value={formData.target_filters.gender}
                        onChange={(e) => handleFormChange('target_filters.gender', e.target.value)}
                        label="Gender Filter"
                      >
                        <MenuItem value="">All</MenuItem>
                        <MenuItem value="male">Male</MenuItem>
                        <MenuItem value="female">Female</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </StepContent>
          </Step>

          <Step>
            <StepLabel>Message Settings</StepLabel>
            <StepContent>
              <Box sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Messages per Minute"
                  type="number"
                  value={formData.messages_per_minute}
                  onChange={(e) => handleFormChange('messages_per_minute', parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 300 }}
                  helperText="Recommended: 10-60 messages per minute to avoid detection"
                />
              </Box>
            </StepContent>
          </Step>
        </Stepper>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          <Box>
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={!isStepValid(activeStep)}
              >
                Create Campaign
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={!isStepValid(activeStep)}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => { onClose(); handleReset(); }}>
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function MessagingDashboard() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const queryClient = useQueryClient();
  const { setCampaigns } = useApp();

  // Fetch campaigns
  const { data: campaignsData, isLoading, error } = useQuery(
    'campaigns',
    campaignsAPI.getAll,
    {
      refetchInterval: 5000, // Refresh every 5 seconds for real-time updates
      onSuccess: (data) => {
        setCampaigns(data.data || []);
      },
      onError: (error) => {
        toast.error('Failed to load campaigns');
      },
    }
  );

  // Create campaign mutation
  const createMutation = useMutation(campaignsAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries('campaigns');
      setCreateDialogOpen(false);
      toast.success('Campaign created successfully');
    },
    onError: (error) => {
      toast.error('Failed to create campaign');
    },
  });

  // Start campaign mutation
  const startMutation = useMutation(
    (campaignId) => campaignsAPI.start(campaignId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('campaigns');
        toast.success('Campaign started successfully');
      },
      onError: (error) => {
        toast.error('Failed to start campaign');
      },
    }
  );

  // Pause campaign mutation
  const pauseMutation = useMutation(
    (campaignId) => campaignsAPI.pause(campaignId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('campaigns');
        toast.success('Campaign paused successfully');
      },
      onError: (error) => {
        toast.error('Failed to pause campaign');
      },
    }
  );

  // Delete campaign mutation
  const deleteMutation = useMutation(campaignsAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries('campaigns');
      toast.success('Campaign deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete campaign');
    },
  });

  const campaigns = Array.isArray(campaignsData?.campaigns)
    ? campaignsData.campaigns
    : Array.isArray(campaignsData?.data)
    ? campaignsData.data
    : Array.isArray(campaignsData)
    ? campaignsData
    : [];

  const handleCreate = (formData) => {
    createMutation.mutate(formData);
  };

  const handleStart = (campaign) => {
    startMutation.mutate(campaign.id);
  };

  const handlePause = (campaign) => {
    pauseMutation.mutate(campaign.id);
  };

  const handleStop = (campaign) => {
    // Stop is same as cancel in our API
    pauseMutation.mutate(campaign.id);
  };

  const handleEdit = (campaign) => {
    // Edit functionality to be implemented
    toast.info('Edit functionality coming soon');
  };

  const handleDelete = (campaign) => {
    if (window.confirm(`Are you sure you want to delete campaign "${campaign.name}"?`)) {
      deleteMutation.mutate(campaign.id);
    }
  };

  const handleViewAnalytics = (campaign) => {
    // Navigate to analytics page with campaign filter
    toast.info('Analytics view coming soon');
  };

  const activeCampaigns = campaigns.filter(c => c.status === 'running');
  const completedCampaigns = campaigns.filter(c => c.status === 'completed');
  const allCampaigns = campaigns;

  if (error) {
    return (
      <Box className="fade-in">
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load campaigns. Please check your connection to the backend.
        </Alert>
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700 }}>
          Messaging Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
          sx={{ textTransform: 'none' }}
        >
          New Campaign
        </Button>
      </Box>

      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label={`All Campaigns (${allCampaigns.length})`} />
          <Tab label={`Active (${activeCampaigns.length})`} />
          <Tab label={`Completed (${completedCampaigns.length})`} />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {allCampaigns.length === 0 && !isLoading ? (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 8 }}>
              <MessageIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                No Campaigns Found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Create your first messaging campaign to get started
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setCreateDialogOpen(true)}
                sx={{ textTransform: 'none' }}
              >
                Create Campaign
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Grid container spacing={3}>
            {allCampaigns.map((campaign) => (
              <Grid item xs={12} sm={6} md={4} key={campaign.id}>
                <CampaignCard
                  campaign={campaign}
                  onStart={handleStart}
                  onPause={handlePause}
                  onStop={handleStop}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onViewAnalytics={handleViewAnalytics}
                />
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {activeCampaigns.map((campaign) => (
            <Grid item xs={12} sm={6} md={4} key={campaign.id}>
              <CampaignCard
                campaign={campaign}
                onStart={handleStart}
                onPause={handlePause}
                onStop={handleStop}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onViewAnalytics={handleViewAnalytics}
              />
            </Grid>
          ))}
        </Grid>
        {activeCampaigns.length === 0 && (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No active campaigns
              </Typography>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          {completedCampaigns.map((campaign) => (
            <Grid item xs={12} sm={6} md={4} key={campaign.id}>
              <CampaignCard
                campaign={campaign}
                onStart={handleStart}
                onPause={handlePause}
                onStop={handleStop}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onViewAnalytics={handleViewAnalytics}
              />
            </Grid>
          ))}
        </Grid>
        {completedCampaigns.length === 0 && (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No completed campaigns
              </Typography>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      {/* Create Campaign Dialog */}
      <CreateCampaignDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreate}
        profiles={profiles}
      />
    </Box>
  );
}

export default MessagingDashboard;
