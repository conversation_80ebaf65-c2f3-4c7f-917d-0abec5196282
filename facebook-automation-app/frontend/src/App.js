import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';

// Layout components
import Sidebar from './components/Layout/Sidebar';
import TopBar from './components/Layout/TopBar';

// Page components
import Dashboard from './pages/Dashboard';
import Profiles from './pages/Profiles';
import Scraping from './pages/Scraping';
import Messaging from './pages/Messaging';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';

// Context providers
import { AppProvider } from './contexts/AppContext';

// Hooks
import useElectronMenu from './hooks/useElectronMenu';

function AppContent() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Handle Electron menu actions (now inside AppProvider)
  useElectronMenu();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <Box className="app-container">
      {/* Sidebar */}
      <Sidebar open={sidebarOpen} onToggle={toggleSidebar} />

      {/* Main Content */}
      <Box className="main-content">
        {/* Top Bar */}
        <TopBar onMenuClick={toggleSidebar} />

        {/* Content Area */}
        <Box className="content-area">
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/profiles" element={<Profiles />} />
            <Route path="/scraping" element={<Scraping />} />
            <Route path="/messaging" element={<Messaging />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Box>
      </Box>
    </Box>
  );
}

function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

export default App;
