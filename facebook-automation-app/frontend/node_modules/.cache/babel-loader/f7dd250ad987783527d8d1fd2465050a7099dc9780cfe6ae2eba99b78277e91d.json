{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\n\n// Layout components\nimport Sidebar from './components/Layout/Sidebar';\nimport TopBar from './components/Layout/TopBar';\n\n// Page components\nimport Dashboard from './pages/Dashboard';\nimport Profiles from './pages/Profiles';\nimport Scraping from './pages/Scraping';\nimport Messaging from './pages/Messaging';\nimport Analytics from './pages/Analytics';\nimport Settings from './pages/Settings';\n\n// Context providers\nimport { AppProvider } from './contexts/AppContext';\n\n// Hooks\nimport useElectronMenu from './hooks/useElectronMenu';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  // Handle Electron menu actions (now inside AppProvider)\n  useElectronMenu();\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      open: sidebarOpen,\n      onToggle: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(TopBar, {\n        onMenuClick: toggleSidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: \"content-area\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profiles\",\n            element: /*#__PURE__*/_jsxDEV(Profiles, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/scraping\",\n            element: /*#__PURE__*/_jsxDEV(Scraping, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/messaging\",\n            element: /*#__PURE__*/_jsxDEV(Messaging, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/analytics\",\n            element: /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settings\",\n            element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_s(AppContent, \"HOofLW7TbQ4679cWtw3pXiqCYSQ=\", false, function () {\n  return [useElectronMenu];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AppProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Routes", "Route", "Navigate", "Box", "Sidebar", "TopBar", "Dashboard", "Profiles", "Scraping", "Messaging", "Analytics", "Settings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useElectronMenu", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "sidebarOpen", "setSidebarOpen", "toggleSidebar", "className", "children", "open", "onToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMenuClick", "path", "element", "to", "replace", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/App.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\n\n// Layout components\nimport Sidebar from './components/Layout/Sidebar';\nimport TopBar from './components/Layout/TopBar';\n\n// Page components\nimport Dashboard from './pages/Dashboard';\nimport Profiles from './pages/Profiles';\nimport Scraping from './pages/Scraping';\nimport Messaging from './pages/Messaging';\nimport Analytics from './pages/Analytics';\nimport Settings from './pages/Settings';\n\n// Context providers\nimport { AppProvider } from './contexts/AppContext';\n\n// Hooks\nimport useElectronMenu from './hooks/useElectronMenu';\n\nfunction AppContent() {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  // Handle Electron menu actions (now inside AppProvider)\n  useElectronMenu();\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  return (\n    <Box className=\"app-container\">\n      {/* Sidebar */}\n      <Sidebar open={sidebarOpen} onToggle={toggleSidebar} />\n\n      {/* Main Content */}\n      <Box className=\"main-content\">\n        {/* Top Bar */}\n        <TopBar onMenuClick={toggleSidebar} />\n\n        {/* Content Area */}\n        <Box className=\"content-area\">\n          <Routes>\n            <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n            <Route path=\"/dashboard\" element={<Dashboard />} />\n            <Route path=\"/profiles\" element={<Profiles />} />\n            <Route path=\"/scraping\" element={<Scraping />} />\n            <Route path=\"/messaging\" element={<Messaging />} />\n            <Route path=\"/analytics\" element={<Analytics />} />\n            <Route path=\"/settings\" element={<Settings />} />\n            <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n          </Routes>\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\nfunction App() {\n  return (\n    <AppProvider>\n      <AppContent />\n    </AppProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,GAAG,QAAQ,eAAe;;AAEnC;AACA,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,MAAM,MAAM,4BAA4B;;AAE/C;AACA,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;;AAEvC;AACA,SAASC,WAAW,QAAQ,uBAAuB;;AAEnD;AACA,OAAOC,eAAe,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACAc,eAAe,CAAC,CAAC;EAEjB,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,oBACEH,OAAA,CAACZ,GAAG;IAACkB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BP,OAAA,CAACX,OAAO;MAACmB,IAAI,EAAEL,WAAY;MAACM,QAAQ,EAAEJ;IAAc;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvDb,OAAA,CAACZ,GAAG;MAACkB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3BP,OAAA,CAACV,MAAM;QAACwB,WAAW,EAAET;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGtCb,OAAA,CAACZ,GAAG;QAACkB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BP,OAAA,CAACf,MAAM;UAAAsB,QAAA,gBACLP,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACb,QAAQ;cAAC8B,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjEb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhB,OAAA,CAACT,SAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhB,OAAA,CAACR,QAAQ;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhB,OAAA,CAACP,QAAQ;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhB,OAAA,CAACN,SAAS;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhB,OAAA,CAACL,SAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhB,OAAA,CAACJ,QAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDb,OAAA,CAACd,KAAK;YAAC6B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACb,QAAQ;cAAC8B,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACX,EAAA,CApCQD,UAAU;EAAA,QAIjBH,eAAe;AAAA;AAAAqB,EAAA,GAJRlB,UAAU;AAsCnB,SAASmB,GAAGA,CAAA,EAAG;EACb,oBACEpB,OAAA,CAACH,WAAW;IAAAU,QAAA,eACVP,OAAA,CAACC,UAAU;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAElB;AAACQ,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}