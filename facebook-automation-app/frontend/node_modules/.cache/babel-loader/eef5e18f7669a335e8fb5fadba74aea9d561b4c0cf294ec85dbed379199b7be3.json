{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Common/TabPanel.js\";\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel({\n  children,\n  value,\n  index,\n  ...other\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `tabpanel-${index}`,\n    \"aria-labelledby\": `tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        pt: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nexport default TabPanel;\nvar _c;\n$RefreshReg$(_c, \"TabPanel\");", "map": {"version": 3, "names": ["React", "Box", "jsxDEV", "_jsxDEV", "TabPanel", "children", "value", "index", "other", "role", "hidden", "id", "sx", "pt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Common/TabPanel.js"], "sourcesContent": ["import React from 'react';\nimport { Box } from '@mui/material';\n\nfunction TabPanel({ children, value, index, ...other }) {\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`tabpanel-${index}`}\n      aria-labelledby={`tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nexport default TabPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,SAASC,QAAQA,CAAC;EAAEC,QAAQ;EAAEC,KAAK;EAAEC,KAAK;EAAE,GAAGC;AAAM,CAAC,EAAE;EACtD,oBACEL,OAAA;IACEM,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,YAAYJ,KAAK,EAAG;IACxB,mBAAiB,OAAOA,KAAK,EAAG;IAAA,GAC5BC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIJ,OAAA,CAACF,GAAG;MAACW,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CAAC;AAEV;AAACC,EAAA,GAZQd,QAAQ;AAcjB,eAAeA,QAAQ;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}