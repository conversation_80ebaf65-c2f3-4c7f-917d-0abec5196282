{"ast": null, "code": "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}", "map": {"version": 3, "names": ["sum", "values", "valueof", "undefined", "value", "index"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/node_modules/d3-array/src/sum.js"], "sourcesContent": ["export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n"], "mappings": "AAAA,eAAe,SAASA,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC3C,IAAIF,GAAG,GAAG,CAAC;EACX,IAAIE,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAIG,KAAK,GAAG,CAACA,KAAK,EAAE;QAClBJ,GAAG,IAAII,KAAK;MACd;IACF;EACF,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAIG,KAAK,GAAG,CAACF,OAAO,CAACE,KAAK,EAAE,EAAEC,KAAK,EAAEJ,MAAM,CAAC,EAAE;QAC5CD,GAAG,IAAII,KAAK;MACd;IACF;EACF;EACA,OAAOJ,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}