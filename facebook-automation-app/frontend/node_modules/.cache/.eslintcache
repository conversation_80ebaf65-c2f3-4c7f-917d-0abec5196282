[{"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/index.js": "1", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/App.js": "2", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Dashboard.js": "3", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Messaging.js": "4", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Analytics.js": "5", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Settings.js": "6", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Scraping.js": "7", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js": "8", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/hooks/useElectronMenu.js": "9", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/contexts/AppContext.js": "10", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/Sidebar.js": "11", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/TopBar.js": "12", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/services/api.js": "13", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Common/TabPanel.js": "14"}, {"size": 3320, "mtime": 1751733990984, "results": "15", "hashOfConfig": "16"}, {"size": 1988, "mtime": 1751767499053, "results": "17", "hashOfConfig": "16"}, {"size": 11108, "mtime": 1751769822845, "results": "18", "hashOfConfig": "16"}, {"size": 23150, "mtime": 1751769871447, "results": "19", "hashOfConfig": "16"}, {"size": 14710, "mtime": 1751766699191, "results": "20", "hashOfConfig": "16"}, {"size": 20218, "mtime": 1751767035532, "results": "21", "hashOfConfig": "16"}, {"size": 24068, "mtime": 1751769895478, "results": "22", "hashOfConfig": "16"}, {"size": 18992, "mtime": 1751734664266, "results": "23", "hashOfConfig": "16"}, {"size": 1578, "mtime": 1751734096260, "results": "24", "hashOfConfig": "16"}, {"size": 10830, "mtime": 1751734081033, "results": "25", "hashOfConfig": "16"}, {"size": 7477, "mtime": 1751734168030, "results": "26", "hashOfConfig": "16"}, {"size": 3648, "mtime": 1751734202604, "results": "27", "hashOfConfig": "16"}, {"size": 5473, "mtime": 1751769377727, "results": "28", "hashOfConfig": "16"}, {"size": 397, "mtime": 1751766963097, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "no1kgn", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/index.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/App.js", ["72"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Dashboard.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Messaging.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Analytics.js", ["73", "74", "75"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Settings.js", ["76", "77", "78", "79", "80", "81", "82"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Scraping.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js", ["83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/hooks/useElectronMenu.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/contexts/AppContext.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/Sidebar.js", ["95"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/TopBar.js", ["96"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/services/api.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Common/TabPanel.js", [], [], {"ruleId": "97", "severity": 1, "message": "98", "line": 1, "column": 17, "nodeType": "99", "messageId": "100", "endLine": 1, "endColumn": 26}, {"ruleId": "97", "severity": 1, "message": "101", "line": 20, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 20, "endColumn": 8}, {"ruleId": "97", "severity": 1, "message": "102", "line": 22, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 22, "endColumn": 8}, {"ruleId": "97", "severity": 1, "message": "103", "line": 23, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 23, "endColumn": 10}, {"ruleId": "97", "severity": 1, "message": "103", "line": 16, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 16, "endColumn": 10}, {"ruleId": "97", "severity": 1, "message": "104", "line": 24, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 24, "endColumn": 13}, {"ruleId": "97", "severity": 1, "message": "105", "line": 34, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 34, "endColumn": 27}, {"ruleId": "97", "severity": 1, "message": "106", "line": 38, "column": 14, "nodeType": "99", "messageId": "100", "endLine": 38, "endColumn": 25}, {"ruleId": "97", "severity": 1, "message": "107", "line": 40, "column": 11, "nodeType": "99", "messageId": "100", "endLine": 40, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "108", "line": 459, "column": 9, "nodeType": "99", "messageId": "100", "endLine": 459, "endColumn": 20}, {"ruleId": "97", "severity": 1, "message": "109", "line": 462, "column": 17, "nodeType": "99", "messageId": "100", "endLine": 462, "endColumn": 31}, {"ruleId": "97", "severity": 1, "message": "98", "line": 1, "column": 27, "nodeType": "99", "messageId": "100", "endLine": 1, "endColumn": 36}, {"ruleId": "97", "severity": 1, "message": "110", "line": 9, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 9, "endColumn": 8}, {"ruleId": "97", "severity": 1, "message": "111", "line": 10, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 10, "endColumn": 12}, {"ruleId": "97", "severity": 1, "message": "112", "line": 11, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 11, "endColumn": 12}, {"ruleId": "97", "severity": 1, "message": "113", "line": 12, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 12, "endColumn": 17}, {"ruleId": "97", "severity": 1, "message": "114", "line": 13, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 13, "endColumn": 12}, {"ruleId": "97", "severity": 1, "message": "115", "line": 14, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 14, "endColumn": 11}, {"ruleId": "97", "severity": 1, "message": "101", "line": 15, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 15, "endColumn": 8}, {"ruleId": "97", "severity": 1, "message": "116", "line": 36, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 36, "endColumn": 10}, {"ruleId": "97", "severity": 1, "message": "117", "line": 45, "column": 11, "nodeType": "99", "messageId": "100", "endLine": 45, "endColumn": 19}, {"ruleId": "97", "severity": 1, "message": "118", "line": 47, "column": 15, "nodeType": "99", "messageId": "100", "endLine": 47, "endColumn": 27}, {"ruleId": "97", "severity": 1, "message": "119", "line": 232, "column": 44, "nodeType": "99", "messageId": "100", "endLine": 232, "endColumn": 65}, {"ruleId": "97", "severity": 1, "message": "103", "line": 12, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 12, "endColumn": 10}, {"ruleId": "97", "severity": 1, "message": "120", "line": 9, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 9, "endColumn": 9}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'Alert' is defined but never used.", "'Divider' is defined but never used.", "'IconButton' is defined but never used.", "'SecurityIcon' is defined but never used.", "'PaletteIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'queryClient' is assigned a value but never used.", "'systemSettings' is assigned a value but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Tooltip' is defined but never used.", "'StopIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'setAppSelectedProfile' is assigned a value but never used.", "'Button' is defined but never used."]