[{"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/index.js": "1", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/App.js": "2", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Dashboard.js": "3", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Messaging.js": "4", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Analytics.js": "5", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Settings.js": "6", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Scraping.js": "7", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js": "8", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/hooks/useElectronMenu.js": "9", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/contexts/AppContext.js": "10", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/Sidebar.js": "11", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/TopBar.js": "12", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/services/api.js": "13", "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Common/TabPanel.js": "14"}, {"size": 3320, "mtime": 1751733990984, "results": "15", "hashOfConfig": "16"}, {"size": 1988, "mtime": 1751767499053, "results": "17", "hashOfConfig": "16"}, {"size": 11174, "mtime": 1751769421160, "results": "18", "hashOfConfig": "16"}, {"size": 23382, "mtime": 1751769498290, "results": "19", "hashOfConfig": "16"}, {"size": 14710, "mtime": 1751766699191, "results": "20", "hashOfConfig": "16"}, {"size": 20218, "mtime": 1751767035532, "results": "21", "hashOfConfig": "16"}, {"size": 24172, "mtime": 1751767206089, "results": "22", "hashOfConfig": "16"}, {"size": 18992, "mtime": 1751734664266, "results": "23", "hashOfConfig": "16"}, {"size": 1578, "mtime": 1751734096260, "results": "24", "hashOfConfig": "16"}, {"size": 10830, "mtime": 1751734081033, "results": "25", "hashOfConfig": "16"}, {"size": 7477, "mtime": 1751734168030, "results": "26", "hashOfConfig": "16"}, {"size": 3648, "mtime": 1751734202604, "results": "27", "hashOfConfig": "16"}, {"size": 5473, "mtime": 1751769377727, "results": "28", "hashOfConfig": "16"}, {"size": 397, "mtime": 1751766963097, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "no1kgn", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/index.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/App.js", ["72"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Dashboard.js", ["73", "74", "75", "76"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Messaging.js", ["77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Analytics.js", ["91", "92", "93"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Settings.js", ["94", "95", "96", "97", "98", "99", "100"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Scraping.js", ["101", "102", "103", "104", "105", "106", "107", "108", "109", "110"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js", ["111", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/hooks/useElectronMenu.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/contexts/AppContext.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/Sidebar.js", ["123"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Layout/TopBar.js", ["124"], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/services/api.js", [], [], "/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/components/Common/TabPanel.js", [], [], {"ruleId": "125", "severity": 1, "message": "126", "line": 1, "column": 17, "nodeType": "127", "messageId": "128", "endLine": 1, "endColumn": 26}, {"ruleId": "125", "severity": 1, "message": "126", "line": 1, "column": 17, "nodeType": "127", "messageId": "128", "endLine": 1, "endColumn": 26}, {"ruleId": "125", "severity": 1, "message": "129", "line": 1, "column": 28, "nodeType": "127", "messageId": "128", "endLine": 1, "endColumn": 36}, {"ruleId": "125", "severity": 1, "message": "130", "line": 29, "column": 15, "nodeType": "127", "messageId": "128", "endLine": 29, "endColumn": 27}, {"ruleId": "125", "severity": 1, "message": "131", "line": 35, "column": 64, "nodeType": "127", "messageId": "128", "endLine": 35, "endColumn": 76}, {"ruleId": "125", "severity": 1, "message": "126", "line": 1, "column": 27, "nodeType": "127", "messageId": "128", "endLine": 1, "endColumn": 36}, {"ruleId": "125", "severity": 1, "message": "132", "line": 20, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 20, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "133", "line": 21, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 21, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "134", "line": 22, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 22, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "135", "line": 23, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 23, "endColumn": 17}, {"ruleId": "125", "severity": 1, "message": "136", "line": 24, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 24, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "137", "line": 25, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 25, "endColumn": 11}, {"ruleId": "125", "severity": 1, "message": "138", "line": 26, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 26, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "139", "line": 47, "column": 11, "nodeType": "127", "messageId": "128", "endLine": 47, "endColumn": 19}, {"ruleId": "125", "severity": 1, "message": "140", "line": 52, "column": 15, "nodeType": "127", "messageId": "128", "endLine": 52, "endColumn": 27}, {"ruleId": "125", "severity": 1, "message": "141", "line": 53, "column": 11, "nodeType": "127", "messageId": "128", "endLine": 53, "endColumn": 19}, {"ruleId": "125", "severity": 1, "message": "142", "line": 57, "column": 17, "nodeType": "127", "messageId": "128", "endLine": 57, "endColumn": 31}, {"ruleId": "125", "severity": 1, "message": "143", "line": 58, "column": 13, "nodeType": "127", "messageId": "128", "endLine": 58, "endColumn": 23}, {"ruleId": "125", "severity": 1, "message": "131", "line": 63, "column": 24, "nodeType": "127", "messageId": "128", "endLine": 63, "endColumn": 36}, {"ruleId": "125", "severity": 1, "message": "138", "line": 20, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 20, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "144", "line": 22, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 22, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "145", "line": 23, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 23, "endColumn": 10}, {"ruleId": "125", "severity": 1, "message": "145", "line": 16, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 16, "endColumn": 10}, {"ruleId": "125", "severity": 1, "message": "146", "line": 24, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 24, "endColumn": 13}, {"ruleId": "125", "severity": 1, "message": "147", "line": 34, "column": 15, "nodeType": "127", "messageId": "128", "endLine": 34, "endColumn": 27}, {"ruleId": "125", "severity": 1, "message": "148", "line": 38, "column": 14, "nodeType": "127", "messageId": "128", "endLine": 38, "endColumn": 25}, {"ruleId": "125", "severity": 1, "message": "149", "line": 40, "column": 11, "nodeType": "127", "messageId": "128", "endLine": 40, "endColumn": 19}, {"ruleId": "125", "severity": 1, "message": "150", "line": 459, "column": 9, "nodeType": "127", "messageId": "128", "endLine": 459, "endColumn": 20}, {"ruleId": "125", "severity": 1, "message": "151", "line": 462, "column": 17, "nodeType": "127", "messageId": "128", "endLine": 462, "endColumn": 31}, {"ruleId": "125", "severity": 1, "message": "126", "line": 1, "column": 27, "nodeType": "127", "messageId": "128", "endLine": 1, "endColumn": 36}, {"ruleId": "125", "severity": 1, "message": "132", "line": 20, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 20, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "133", "line": 21, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 21, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "134", "line": 22, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 22, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "135", "line": 23, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 23, "endColumn": 17}, {"ruleId": "125", "severity": 1, "message": "136", "line": 24, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 24, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "137", "line": 25, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 25, "endColumn": 11}, {"ruleId": "125", "severity": 1, "message": "138", "line": 26, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 26, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "152", "line": 40, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 40, "endColumn": 10}, {"ruleId": "125", "severity": 1, "message": "153", "line": 56, "column": 13, "nodeType": "127", "messageId": "128", "endLine": 56, "endColumn": 23}, {"ruleId": "125", "severity": 1, "message": "126", "line": 1, "column": 27, "nodeType": "127", "messageId": "128", "endLine": 1, "endColumn": 36}, {"ruleId": "125", "severity": 1, "message": "132", "line": 9, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 9, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "133", "line": 10, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 10, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "134", "line": 11, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 11, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "135", "line": 12, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 12, "endColumn": 17}, {"ruleId": "125", "severity": 1, "message": "136", "line": 13, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 13, "endColumn": 12}, {"ruleId": "125", "severity": 1, "message": "137", "line": 14, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 14, "endColumn": 11}, {"ruleId": "125", "severity": 1, "message": "138", "line": 15, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 15, "endColumn": 8}, {"ruleId": "125", "severity": 1, "message": "152", "line": 36, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 36, "endColumn": 10}, {"ruleId": "125", "severity": 1, "message": "139", "line": 45, "column": 11, "nodeType": "127", "messageId": "128", "endLine": 45, "endColumn": 19}, {"ruleId": "125", "severity": 1, "message": "154", "line": 47, "column": 15, "nodeType": "127", "messageId": "128", "endLine": 47, "endColumn": 27}, {"ruleId": "125", "severity": 1, "message": "155", "line": 232, "column": 44, "nodeType": "127", "messageId": "128", "endLine": 232, "endColumn": 65}, {"ruleId": "125", "severity": 1, "message": "145", "line": 12, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 12, "endColumn": 10}, {"ruleId": "125", "severity": 1, "message": "156", "line": 9, "column": 3, "nodeType": "127", "messageId": "128", "endLine": 9, "endColumn": 9}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useState' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'messagingAPI' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'StopIcon' is defined but never used.", "'TemplateIcon' is defined but never used.", "'SendIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "'Alert' is defined but never used.", "'Divider' is defined but never used.", "'IconButton' is defined but never used.", "'SecurityIcon' is defined but never used.", "'PaletteIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'queryClient' is assigned a value but never used.", "'systemSettings' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'PersonIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'setAppSelectedProfile' is assigned a value but never used.", "'Button' is defined but never used."]